import { z } from 'zod';
import { ExerciseMediaTypeSchema } from '../inputTypeSchemas/ExerciseMediaTypeSchema'
import { ControlExerciseWithRelationsSchema, ControlExercisePartialWithRelationsSchema, ControlExerciseOptionalDefaultsWithRelationsSchema } from './ControlExerciseSchema'
import type { ControlExerciseWithRelations, ControlExercisePartialWithRelations, ControlExerciseOptionalDefaultsWithRelations } from './ControlExerciseSchema'

/////////////////////////////////////////
// CONTROL EXERCISE MEDIA SCHEMA
/////////////////////////////////////////

export const ControlExerciseMediaSchema = z.object({
  type: ExerciseMediaTypeSchema,
  id: z.string(),
  fileName: z.string().nullable(),
  fileType: z.string().nullable(),
  data: z.instanceof(Buffer).nullable(),
  fileUrl: z.string().nullable(),
  controlExerciseId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type ControlExerciseMedia = z.infer<typeof ControlExerciseMediaSchema>

/////////////////////////////////////////
// CONTROL EXERCISE MEDIA PARTIAL SCHEMA
/////////////////////////////////////////

export const ControlExerciseMediaPartialSchema = ControlExerciseMediaSchema.partial()

export type ControlExerciseMediaPartial = z.infer<typeof ControlExerciseMediaPartialSchema>

/////////////////////////////////////////
// CONTROL EXERCISE MEDIA OPTIONAL DEFAULTS SCHEMA
/////////////////////////////////////////

export const ControlExerciseMediaOptionalDefaultsSchema = ControlExerciseMediaSchema.merge(z.object({
  id: z.string().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type ControlExerciseMediaOptionalDefaults = z.infer<typeof ControlExerciseMediaOptionalDefaultsSchema>

/////////////////////////////////////////
// CONTROL EXERCISE MEDIA RELATION SCHEMA
/////////////////////////////////////////

export type ControlExerciseMediaRelations = {
  controlExercise: ControlExerciseWithRelations;
};

export type ControlExerciseMediaWithRelations = z.infer<typeof ControlExerciseMediaSchema> & ControlExerciseMediaRelations

export const ControlExerciseMediaWithRelationsSchema: z.ZodType<ControlExerciseMediaWithRelations> = ControlExerciseMediaSchema.merge(z.object({
  controlExercise: z.lazy(() => ControlExerciseWithRelationsSchema),
}))

/////////////////////////////////////////
// CONTROL EXERCISE MEDIA OPTIONAL DEFAULTS RELATION SCHEMA
/////////////////////////////////////////

export type ControlExerciseMediaOptionalDefaultsRelations = {
  controlExercise: ControlExerciseOptionalDefaultsWithRelations;
};

export type ControlExerciseMediaOptionalDefaultsWithRelations = z.infer<typeof ControlExerciseMediaOptionalDefaultsSchema> & ControlExerciseMediaOptionalDefaultsRelations

export const ControlExerciseMediaOptionalDefaultsWithRelationsSchema: z.ZodType<ControlExerciseMediaOptionalDefaultsWithRelations> = ControlExerciseMediaOptionalDefaultsSchema.merge(z.object({
  controlExercise: z.lazy(() => ControlExerciseOptionalDefaultsWithRelationsSchema),
}))

/////////////////////////////////////////
// CONTROL EXERCISE MEDIA PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type ControlExerciseMediaPartialRelations = {
  controlExercise?: ControlExercisePartialWithRelations;
};

export type ControlExerciseMediaPartialWithRelations = z.infer<typeof ControlExerciseMediaPartialSchema> & ControlExerciseMediaPartialRelations

export const ControlExerciseMediaPartialWithRelationsSchema: z.ZodType<ControlExerciseMediaPartialWithRelations> = ControlExerciseMediaPartialSchema.merge(z.object({
  controlExercise: z.lazy(() => ControlExercisePartialWithRelationsSchema),
})).partial()

export type ControlExerciseMediaOptionalDefaultsWithPartialRelations = z.infer<typeof ControlExerciseMediaOptionalDefaultsSchema> & ControlExerciseMediaPartialRelations

export const ControlExerciseMediaOptionalDefaultsWithPartialRelationsSchema: z.ZodType<ControlExerciseMediaOptionalDefaultsWithPartialRelations> = ControlExerciseMediaOptionalDefaultsSchema.merge(z.object({
  controlExercise: z.lazy(() => ControlExercisePartialWithRelationsSchema),
}).partial())

export type ControlExerciseMediaWithPartialRelations = z.infer<typeof ControlExerciseMediaSchema> & ControlExerciseMediaPartialRelations

export const ControlExerciseMediaWithPartialRelationsSchema: z.ZodType<ControlExerciseMediaWithPartialRelations> = ControlExerciseMediaSchema.merge(z.object({
  controlExercise: z.lazy(() => ControlExercisePartialWithRelationsSchema),
}).partial())

export default ControlExerciseMediaSchema;

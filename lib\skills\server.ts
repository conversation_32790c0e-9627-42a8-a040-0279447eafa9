import { SkillWithRelationsSchema } from '@/prisma/generated/zod/modelSchema/SkillSchema'
import { z } from 'zod'
import prisma from '@/lib/prisma-client'

// Fetch all skills
export async function fetchSkills(): Promise<
    z.infer<typeof SkillWithRelationsSchema>[]
> {
    return (await prisma.skill.findMany({
        include: {
            domain: true,
            level: true
        }
    })) as z.infer<typeof SkillWithRelationsSchema>[]
}

export async function fetchSkill(id: string) {
    return await prisma.skill.findUnique({
        where: { id }
    })
}

export async function fetchSkillsByIds(ids: string[]) {
    return await prisma.skill.findMany({
        where: { id: { in: ids } }
    })
}

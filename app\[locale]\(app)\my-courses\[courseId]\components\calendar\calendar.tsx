'use client'
import { Calendar, momentLocalizer } from 'react-big-calendar'
import moment from 'moment'
import 'moment/locale/fr'
import 'moment/locale/ar'
import 'moment/locale/en-gb'
import 'react-big-calendar/lib/css/react-big-calendar.css'

import React, { useEffect, useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { getLangDir } from 'rtl-detect'
import { useLocale } from 'next-intl'
import { useParams } from 'next/navigation'

import { getPlannedControlsByClassIdAndDatePlanned } from '@/lib/control-mode/actions'
import {
    PlannedEvaluationPartial,
    PlannedEvaluationPartialWithRelations
} from '@/prisma/generated/zod/modelSchema/PlannedEvaluationSchema'
import { ClassSSE } from '@/lib/sse/class-events'

const localizer = momentLocalizer(moment)

export default function EditeurCalendar({
    triggerHighlight
}: {
    triggerHighlight: () => void
}) {
    const locale = useLocale()
    const direction = getLangDir(locale)
    moment.locale(locale)

    const params = useParams<{ courseId: string }>()
    const [currentDate, setCurrentDate] = useState(new Date())

    // 🥇 Using TanStack Query
    const { data, isLoading, error, refetch } = useQuery({
        queryKey: ['planned-controls', params.courseId],
        queryFn: async () => {
            const events = await getPlannedControlsByClassIdAndDatePlanned(
                params.courseId
            )
            if ('error' in events) throw new Error('No events found')
            return (events as PlannedEvaluationPartialWithRelations[]).map(
                e => ({
                    ...e,
                    title: e.control?.name ?? '--',
                    description: e.control?.description ?? '',
                    color: '#FF5E0E'
                })
            )
        },
        enabled: !!params.courseId
    })

    useEffect(() => {
        const source = ClassSSE.listen<any>(
            params.courseId,
            'evaluations',
            async payload => {
                await refetch()
                triggerHighlight()
            }
        )

        return () => {
            source.close()
        }
    }, [])

    if (isLoading)
        return (
            <div className="flex justify-center items-center size-full">
                <div className="animate-spin text-3xl">⏳</div>
            </div>
        )
    if (error) return <div className="text-dinoBotRed ">{error.message}</div>

    return (
        <Calendar
            localizer={localizer}
            events={data || []}
            startAccessor="availableDate"
            endAccessor="dueDate"
            style={{ height: 500 }}
            defaultView="month"
            rtl={direction === 'rtl'}
            eventPropGetter={event => ({
                style: {
                    backgroundColor: event.color,
                    borderRadius: '5px',
                    opacity: 0.8,
                    color: 'white',
                    border: 0,
                    display: 'block'
                }
            })}
            date={currentDate}
            onNavigate={date => setCurrentDate(date)}
        />
    )
}

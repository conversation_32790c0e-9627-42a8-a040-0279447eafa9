'server-only'
import { FeatureFlagName } from '@prisma/client'
import prisma from '../prisma-client'

export const getFeatureFlags = async () => {
    return await prisma.featureFlag.findMany()
}

export const getFeaturesFlagsByName = async (name: FeatureFlagName) => {
    return await prisma.featureFlag.findUnique({
        where: {
            featureName: name
        },
        select: {
            isEnabled: true
        }
    })
}

const featuresRedirect: Record<FeatureFlagName, string> = {
    [FeatureFlagName.STUDENT_CHAT_MODE]: '',
    [FeatureFlagName.STUDENT_CLASSES_VIEW]: 'my-courses',
    [FeatureFlagName.STUDENT_EXERCISE_MODE]: 'exercise',
    [FeatureFlagName.STUDENT_EVALUATION_MODE]: 'create-controle',
    [FeatureFlagName.STUDENT_EXAM_MODE]: 'exam'
}

/**
 * Retrieves feature routes based on the feature flag name
 *
 * @param name - The feature flag name to check
 * @returns An array of route strings. Returns empty array if feature is enabled,
 *          an alternative route if another feature is enabled, or ["not-found"] if no alternatives exist
 */

export const getFeatureRoutesByFeatureName = async (name: FeatureFlagName) => {
    const features = await prisma.featureFlag.findMany({
        where: {
            OR: [{ featureName: name }, { isEnabled: true }]
        },
        select: { featureName: true, isEnabled: true },
        orderBy: {
            rang: 'asc'
        }
    })

    const requestedFeature = features.find(f => f.featureName === name)

    if (requestedFeature?.isEnabled) return []

    const alternativeFeature = features.find(
        f => f.isEnabled && f.featureName !== name
    )
    const redirectRoute = alternativeFeature
        ? featuresRedirect[alternativeFeature.featureName]
        : null

    return redirectRoute != null ? [redirectRoute] : ['not-found']
}

export const getFeaturesFlagsByNames = async (names: FeatureFlagName[]) => {
    return await prisma.featureFlag.findMany({
        where: {
            featureName: {
                in: names
            }
        },
        select: {
            featureName: true,
            isEnabled: true
        }
    })
}

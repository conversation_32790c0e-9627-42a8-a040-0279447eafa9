import { checkAuth } from '../utils/auth-utils'
import { logger } from '@/logger/logger'

export function withAuth<Args extends unknown[], Return>(
    fn: (...args: Args) => Promise<Return> | Return
): (...args: Args) => Promise<Return> {
    return async (...args: Args) => {
        const session = await checkAuth()
        logger.debug(
            `${session.user?.email} authenticated for function: ${fn.name}`
        )
        return await fn(...args)
    }
}

export function withUserAuth<Args extends unknown[], Return>(
    fn: (userId: string, ...args: Args) => Promise<Return> | Return
): (...args: Args) => Promise<Return> {
    return async (...args: Args) => {
        const session = await checkAuth()
        if (!session.user?.id) {
            throw new Error('Unauthorized: No user ID in session')
        }
        logger.debug(
            `User ${session.user?.email} authenticated for function: ${fn.name}`
        )
        return await fn(session.user.id, ...args)
    }
}

/*
  Warnings:

  - You are about to drop the `praxeo_task_skill` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `examples` to the `praxeo_tasks` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "praxeo_task_skill" DROP CONSTRAINT "praxeo_task_skill_praxeo_task_id_fkey";

-- DropForeignKey
ALTER TABLE "praxeo_task_skill" DROP CONSTRAINT "praxeo_task_skill_skillId_fkey";

-- DropForeignKey
ALTER TABLE "praxeo_task_skill" DROP CONSTRAINT "praxeo_task_skill_skill_id_fkey";

-- AlterTable
ALTER TABLE "praxeo_tasks" ADD COLUMN     "examples" JSONB NOT NULL;

-- DropTable
DROP TABLE "praxeo_task_skill";

-- CreateTable
CREATE TABLE "praxeo_task_skills" (
    "id" SERIAL NOT NULL,
    "praxeo_task_id" INTEGER NOT NULL,
    "tm_skill_id" TEXT NOT NULL,

    CONSTRAINT "praxeo_task_skills_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "praxeo_task_skills" ADD CONSTRAINT "praxeo_task_skills_praxeo_task_id_fkey" FOREIGN KEY ("praxeo_task_id") REFERENCES "praxeo_tasks"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "praxeo_task_skills" ADD CONSTRAINT "praxeo_task_skills_tm_skill_id_fkey" FOREIGN KEY ("tm_skill_id") REFERENCES "tm_skills"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

import { z } from 'zod';
import { FeatureFlagNameSchema } from '../inputTypeSchemas/FeatureFlagNameSchema'
import { UserWithRelationsSchema, UserPartialWithRelationsSchema, UserOptionalDefaultsWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations, UserOptionalDefaultsWithRelations } from './UserSchema'

/////////////////////////////////////////
// FEATURE FLAG SCHEMA
/////////////////////////////////////////

export const FeatureFlagSchema = z.object({
  featureName: FeatureFlagNameSchema,
  id: z.string(),
  isEnabled: z.boolean(),
  description: z.string().nullable(),
  rang: z.number().nullable(),
  updatedById: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type FeatureFlag = z.infer<typeof FeatureFlagSchema>

/////////////////////////////////////////
// FEATURE FLAG PARTIAL SCHEMA
/////////////////////////////////////////

export const FeatureFlagPartialSchema = FeatureFlagSchema.partial()

export type FeatureFlagPartial = z.infer<typeof FeatureFlagPartialSchema>

/////////////////////////////////////////
// FEATURE FLAG OPTIONAL DEFAULTS SCHEMA
/////////////////////////////////////////

export const FeatureFlagOptionalDefaultsSchema = FeatureFlagSchema.merge(z.object({
  id: z.string().optional(),
  isEnabled: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type FeatureFlagOptionalDefaults = z.infer<typeof FeatureFlagOptionalDefaultsSchema>

/////////////////////////////////////////
// FEATURE FLAG RELATION SCHEMA
/////////////////////////////////////////

export type FeatureFlagRelations = {
  updatedBy?: UserWithRelations | null;
};

export type FeatureFlagWithRelations = z.infer<typeof FeatureFlagSchema> & FeatureFlagRelations

export const FeatureFlagWithRelationsSchema: z.ZodType<FeatureFlagWithRelations> = FeatureFlagSchema.merge(z.object({
  updatedBy: z.lazy(() => UserWithRelationsSchema).nullable(),
}))

/////////////////////////////////////////
// FEATURE FLAG OPTIONAL DEFAULTS RELATION SCHEMA
/////////////////////////////////////////

export type FeatureFlagOptionalDefaultsRelations = {
  updatedBy?: UserOptionalDefaultsWithRelations | null;
};

export type FeatureFlagOptionalDefaultsWithRelations = z.infer<typeof FeatureFlagOptionalDefaultsSchema> & FeatureFlagOptionalDefaultsRelations

export const FeatureFlagOptionalDefaultsWithRelationsSchema: z.ZodType<FeatureFlagOptionalDefaultsWithRelations> = FeatureFlagOptionalDefaultsSchema.merge(z.object({
  updatedBy: z.lazy(() => UserOptionalDefaultsWithRelationsSchema).nullable(),
}))

/////////////////////////////////////////
// FEATURE FLAG PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type FeatureFlagPartialRelations = {
  updatedBy?: UserPartialWithRelations | null;
};

export type FeatureFlagPartialWithRelations = z.infer<typeof FeatureFlagPartialSchema> & FeatureFlagPartialRelations

export const FeatureFlagPartialWithRelationsSchema: z.ZodType<FeatureFlagPartialWithRelations> = FeatureFlagPartialSchema.merge(z.object({
  updatedBy: z.lazy(() => UserPartialWithRelationsSchema).nullable(),
})).partial()

export type FeatureFlagOptionalDefaultsWithPartialRelations = z.infer<typeof FeatureFlagOptionalDefaultsSchema> & FeatureFlagPartialRelations

export const FeatureFlagOptionalDefaultsWithPartialRelationsSchema: z.ZodType<FeatureFlagOptionalDefaultsWithPartialRelations> = FeatureFlagOptionalDefaultsSchema.merge(z.object({
  updatedBy: z.lazy(() => UserPartialWithRelationsSchema).nullable(),
}).partial())

export type FeatureFlagWithPartialRelations = z.infer<typeof FeatureFlagSchema> & FeatureFlagPartialRelations

export const FeatureFlagWithPartialRelationsSchema: z.ZodType<FeatureFlagWithPartialRelations> = FeatureFlagSchema.merge(z.object({
  updatedBy: z.lazy(() => UserPartialWithRelationsSchema).nullable(),
}).partial())

export default FeatureFlagSchema;

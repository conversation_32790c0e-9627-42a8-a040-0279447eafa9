// type SSEClients = Record<string, ReadableStreamDefaultController[]>;

// if (!globalThis.sseClients) {
//     globalThis.sseClients = {};
// }

// declare global {
//     var sseClients: SSEClients;
// }

// export async function GET(req: Request, {
//     params,
// }: {
//     params: Promise<{ classId: string }>
// }): Promise<Response> {
//     const url = new URL(req.url);
//     const eventName = url.searchParams.get("event") || "all";
//     const classId = (await params).classId
//     console.log("🚀 ~ GET ~ classId:", classId)

//     if (!classId) {
//         return new Response("Missing classId", { status: 400 });
//     }
//     (req.signal as any).setMaxListeners?.(9999);
//     const encoder = new TextEncoder();

//     const stream = new ReadableStream({
//         start(controller) {
//             if (!globalThis.sseClients[classId]) {
//                 globalThis.sseClients[classId] = [];
//             }

//             globalThis.sseClients[classId].push(controller);

//             // clean up on close
//             req.signal.addEventListener("abort", () => {
//                 globalThis.sseClients[classId] = globalThis.sseClients[classId].filter(
//                     (c) => c !== controller
//                 );
//                 controller.close();
//             });

//             controller.enqueue(encoder.encode(`: connected to class ${classId}\n\n`));
//         },
//     });

//     return new Response(stream, {
//         headers: {
//             "Content-Type": "text/event-stream",
//             "Cache-Control": "no-cache",
//             "Connection": "keep-alive",
//         },
//     });
// }


type SSEClients = Record<string, Record<string, ReadableStreamDefaultController[]>>;

if (!globalThis.sseClients) {
    globalThis.sseClients = {};
}

declare global {
    var sseClients: SSEClients;
}

export async function GET(req: Request, {
    params,
}: {
    params: Promise<{ classId: string }>
}): Promise<Response> {
    const classId = (await params).classId;
    const url = new URL(req.url);
    const eventName = url.searchParams.get("event") || "all";

    console.log("🚀 ~ GET ~ classId:", classId, "event:", eventName);

    if (!classId) {
        return new Response("Missing classId", { status: 400 });
    }

    (req.signal as any).setMaxListeners?.(9999);
    const encoder = new TextEncoder();

    const stream = new ReadableStream({
        start(controller) {
            if (!globalThis.sseClients[classId]) {
                globalThis.sseClients[classId] = {};
            }
            if (!globalThis.sseClients[classId][eventName]) {
                globalThis.sseClients[classId][eventName] = [];
            }

            globalThis.sseClients[classId][eventName].push(controller);

            // Clean up on close
            req.signal.addEventListener("abort", () => {
                globalThis.sseClients[classId][eventName] =
                    globalThis.sseClients[classId][eventName].filter((c) => c !== controller);
                controller.close();
            });

            controller.enqueue(encoder.encode(`: connected to class ${classId} event ${eventName}\n\n`));
        },
    });

    return new Response(stream, {
        headers: {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    });
}

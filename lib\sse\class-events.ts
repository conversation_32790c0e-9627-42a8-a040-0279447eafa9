


"use client"

export type ClassSSEEvents = "class:scheduled-evaluation" | "class:updated-evaluation" | "class:deleted-evaluation" | "class:event-created" | "class:event-updated" | "class:event-deleted"

export type ClassSSEPayload<T> = {
    classIds: string[];
    event: ClassSSEEvents;
    message?: string;
    data?: T;
};

export class ClassSSE {
    static async emit<T>(classIds: string | string[], payload: Omit<ClassSSEPayload<T>, 'classIds'>): Promise<void> {
        try {
            if (!Array.isArray(classIds)) {
                classIds = [classIds];
            }
            const data: ClassSSEPayload<T> = {
                classIds,
                ...payload
            };
            const res = await fetch(`/api/sse/class/emit`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            });

            if (!res.ok) {
                console.error(`Failed to emit event to class ${classIds}`, await res.text());
            }
        } catch (err) {
            console.error(`Error emitting event to class ${classIds}`, err);
        }
    }

    static listen<T>(
        classId: string,
        event: "evaluations" | "events",
        callback: (payload: ClassSSEPayload<T>) => void | Promise<void>
    ): EventSource {
        const url = `/api/sse/class/listen/${classId}?event=${encodeURIComponent(event)}`;
        const source = new EventSource(url);

        source.addEventListener(event, (e) => {
            try {
                const data: ClassSSEPayload<T> = JSON.parse((e as MessageEvent).data);
                callback(data);
            } catch (err) {
                console.error(`Error parsing SSE message for event "${event}":`, err);
            }
        });

        source.onerror = (err) => {
            console.error(`SSE error on class ${classId} event "${event}":`, err);
        };

        return source; // allow caller to close() when needed
    }
}

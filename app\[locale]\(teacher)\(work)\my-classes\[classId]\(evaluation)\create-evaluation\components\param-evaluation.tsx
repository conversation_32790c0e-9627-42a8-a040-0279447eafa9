'use client'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { useRouter } from '@/i18n/routing'
import { getClassById } from '@/lib/control-mode/services/class/actions'
import { useEvaluationParamsStore } from '@/app/[locale]/(teacher)/(work)/my-classes/[classId]/(evaluation)/store/evaluation-params.store'
import { createTheme, getThemesByClassId } from '@/lib/theme/actions'
import { useParams } from 'next/navigation'
import React, {
    KeyboardEvent,
    useEffect,
    useReducer,
    useRef,
    useTransition,
    useCallback
} from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { Loader2, X } from 'lucide-react'
import { ThemePartial } from '@/prisma/generated/zod/modelSchema/ThemeSchema'
import ControlScoringTypeSchema from '@/prisma/generated/zod/inputTypeSchemas/ControlScoringTypeSchema'
import { zodResolver } from '@hookform/resolvers/zod'
import {
    Domain,
    DomainPartialWithRelationsSchema
} from '@/prisma/generated/zod/modelSchema/DomainSchema'
import {
    Level,
    LevelPartialWithRelationsSchema
} from '@/prisma/generated/zod/modelSchema/LevelSchema'
import EvaluationTypeSchema from '@/prisma/generated/zod/inputTypeSchemas/EvaluationTypeSchema'
import { useTranslations, useLocale } from 'next-intl'
import { z } from 'zod'
import {
    ClassPartial,
    ClassPartialSchema,
    ClassPartialWithRelations
} from '@/prisma/generated/zod/modelSchema/ClassSchema'
import {
    ControlPartialWithRelationsSchema,
    ControlWithPartialRelations
} from '@/prisma/generated/zod/modelSchema/ControlSchema'

type ParamEvaluationProps = {
    isUpdate: boolean
    setOpen?: React.Dispatch<React.SetStateAction<boolean>>
}
const ControlParamEvaluationSchema = z.object({
    name: z.string().optional(),
    assignedClass: z
        .object({
            id: z.string().optional(),
            name: z.string().optional(),
            domain: z
                .object({
                    id: z.number().optional(),
                    name: z.string().optional()
                })
                .nullable()
                .optional(),
            level: z
                .object({
                    id: z.number().optional(),
                    name: z.string().optional()
                })
                .nullable()
                .optional(),
            controls: z
                .array(
                    z.object({
                        id: z.string().optional(),
                        name: z.string().optional()
                    })
                )
                .optional()
        })
        .optional(),
    assignedClassId: z.string().optional(),
    scoringType: ControlScoringTypeSchema.optional(),
    letterRange: z.string().optional(),
    type: EvaluationTypeSchema.optional().optional(),
    themeId: z.string().optional(),
    fontName: z.string().optional(),
    fontSize: z.number().optional()
})

type ParamEvaluationFormType = z.infer<typeof ControlParamEvaluationSchema> // Use ControlParamEvaluationSchema

type ParamEvaluationState = {
    themesP: ThemePartial[]
    isAddingTheme: boolean
    newThemeName: string
}
const ParamEvaluation = ({ isUpdate, setOpen }: ParamEvaluationProps) => {
    const locale = useLocale()
    const t = useTranslations('teacher.myClass.evaluation.params')
    const {
        setIsOnLoadExo,
        setEvalProps,
        evalProps,
        updateEvalProps,
        setLevelAndDomain,
        domains,
        themes,
        level // Destructure level from the store
    } = useEvaluationParamsStore()

    const form = useForm<ParamEvaluationFormType>({
        resolver: zodResolver(ControlParamEvaluationSchema), // Use ControlParamEvaluationSchema
        defaultValues: {
            ...(isUpdate ? evalProps : {}),
            name: evalProps?.name || '',
            assignedClass: evalProps?.assignedClass || {},
            assignedClassId: evalProps?.assignedClassId || '',
            scoringType:
                evalProps?.scoringType || ControlScoringTypeSchema.Enum.NUMERIC,
            letterRange: evalProps?.letterRange || '20',
            type: evalProps?.type || EvaluationTypeSchema.enum.FORMATIVE,
            themeId: evalProps?.themeId || '',
            fontName: evalProps?.fontName || 'Roboto',
            fontSize: evalProps?.fontSize || 12
        }
    })
    const route = useRouter()
    const {
        control,
        handleSubmit,
        formState: { errors },
        setValue,
        watch
    } = form
    const [isOnSubmit, onTransitionSubmit] = useTransition()
    const params = useParams<{ classId: string }>()
    const initState: ParamEvaluationState = {
        themesP: themes,
        isAddingTheme: false,
        newThemeName: ''
    }
    const [{ isAddingTheme, newThemeName, themesP }, dispatch] = useReducer(
        (
            state: ParamEvaluationState,
            action: Partial<ParamEvaluationState>
        ) => ({ ...state, ...action }),
        initState
    )
    const typeEvals = [
        t('formative'),
        t('diagnostic'),
        t('summative'),
        t('certifying')
    ]
    const typeEvalutations = EvaluationTypeSchema.options.map(
        (type: any, i: any) => ({
            title: typeEvals[i],
            value: type
        })
    )
    const notations = [
        {
            title: t('total'),
            value: ControlScoringTypeSchema.Enum.NUMERIC,
            content: Array.from({ length: 10 }, (_, i) => (i + 1) * 10)
        } /*,{title:'Échelle qualitative',value:ControlScoringTypeSchema.Enum.LETTER,content:['A-F']}*/
    ]
    const fontSizes = Array.from({ length: 15 }, (_, i) => 12 + i * 2)
    const googleFonts = [
        'Roboto',
        'Open Sans',
        'Lato',
        'Montserrat',
        'Oswald',
        'Source Sans Pro',
        'Slabo 27px',
        'Raleway',
        'PT Sans',
        'Merriweather',
        'Noto Sans',
        'Nunito',
        'Comfortaa',
        'Ubuntu',
        'Roboto Condensed',
        'Playfair Display',
        'Poppins',
        'Dancing Script',
        'Arimo',
        'Cabin',
        'Fjalla One',
        'Inconsolata',
        'Bitter',
        'Work Sans',
        'Zilla Slab',
        'Overpass',
        'Exo 2',
        'Quicksand',
        'Anton',
        'Karla',
        'Josefin Sans',
        'Libre Baskerville',
        'Pacifico',
        'Varela Round',
        'Baloo 2',
        'Signika',
        'Rubik',
        'Archivo Narrow',
        'Titillium Web',
        'Abel',
        'Catamaran',
        'Asap',
        'Cairo',
        'Fira Sans',
        'Mulish',
        'Manrope',
        'Barlow',
        'Spectral',
        'Heebo'
    ]
    const inputRef = useRef<HTMLInputElement>(null)
    // Removed classDataRef as it's no longer needed for accessing level
    const fetchAssignedClass = useCallback(async () => {
        try {
            const id = params.classId
            const dataClass = await getClassById(id)
            if (dataClass) {
                setValue('assignedClass', dataClass)
            }
        } catch (error) {
            console.error(error)
        }
    }, [params.classId, setValue])

    useEffect(() => {
        fetchAssignedClass()
    }, [fetchAssignedClass])

    const fetchThemes = useCallback(async () => {
        try {
            const data = await getThemesByClassId(params.classId)
            if ('error' in data) throw new Error(data.error)
            dispatch({ themesP: data })
        } catch {
            toast.error(t('error_theme'))
        }
    }, [params.classId, t])
    useEffect(() => {
        fetchThemes()
    }, [fetchThemes])
    const handleAddTheme = async () => {
        if (newThemeName) {
            try {
                const theme = await createTheme({
                    name: newThemeName,
                    classId: params.classId
                })
                if ('error' in theme) throw new Error(theme.error)
                setValue('themeId', theme.id)
                dispatch({
                    themesP: [...themesP, theme],
                    isAddingTheme: false,
                    newThemeName: ''
                })
            } catch {
                toast.error(t('error_create_theme'))
            }
        }
    }

    const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
            e.preventDefault()
            handleAddTheme()
        }
    }

    const cancelAddTheme = () => {
        dispatch({ isAddingTheme: false, newThemeName: '' })
    }

    const onSubmit: SubmitHandler<ParamEvaluationFormType> = data => {
        onTransitionSubmit(() => {
            const assignedClass = watch('assignedClass')
            data = { ...data, assignedClassId: assignedClass?.id }
            setLevelAndDomain(
                data.assignedClass?.level! as Level,
                data.assignedClass?.domain! as Domain
            )
            setIsOnLoadExo(false)
            if (isUpdate) {
                updateEvalProps(data as unknown as ControlWithPartialRelations)
                setOpen?.(false)
                toast.message(t('submite_message'))
            } else {
                setEvalProps(data as unknown as ControlWithPartialRelations)
                route.push(`create-evaluation-next`)
            }
        })
    }
    return (
        <div>
            <Form {...form}>
                <form
                    onSubmit={handleSubmit(onSubmit)}
                    className="flex flex-col gap-3 w-3/5 mx-auto"
                >
                    <FormField
                        name="name"
                        control={control}
                        rules={{ required: t('form_name_error') }}
                        render={({ field }) => (
                            <FormItem className=" flex items-center space-y-1 w-full gap-4">
                                <FormLabel className="min-w-40 text-left">
                                    {t('form_name')}
                                </FormLabel>
                                <div className="flex-1">
                                    <FormControl>
                                        <Input
                                            {...field}
                                            onFocus={e => {
                                                setTimeout(() => {
                                                    const length =
                                                        e.target.value.length
                                                    e.target.setSelectionRange(
                                                        length,
                                                        length
                                                    )
                                                }, 0)
                                            }}
                                            placeholder={t(
                                                'form_name_placeholder'
                                            )}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </div>
                            </FormItem>
                        )}
                    />
                    <FormField
                        name="assignedClass"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                            <FormItem className=" flex items-center space-y-1 w-full gap-4">
                                <FormLabel className={`min-w-40 text-left`}>
                                    {t('form_class')}
                                </FormLabel>
                                <FormControl className="flex-1">
                                    <p className="px-3 py-1 rounded-md border border-input bg-background">
                                        {field.value?.name}
                                    </p>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <div className="flex gap-2 w-full">
                        <FormField
                            name="scoringType"
                            control={control}
                            rules={{ required: t('form_notation_error') }}
                            render={({ field }) => (
                                <FormItem className="flex items-center space-y-1 w-full gap-4">
                                    <FormLabel className={`min-w-40 text-left`}>
                                        {t('form_notation')}
                                    </FormLabel>
                                    <div className="flex-1">
                                        <Select
                                            onValueChange={val => {
                                                field.onChange(val)
                                            }}
                                            value={field.value}
                                        >
                                            <FormControl className="flex-1">
                                                <SelectTrigger>
                                                    <SelectValue
                                                        placeholder={t(
                                                            'form_notation_placeholder'
                                                        )}
                                                    />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                {notations.map(
                                                    (notation, index) => (
                                                        <SelectItem
                                                            key={index}
                                                            value={
                                                                notation.value
                                                            }
                                                        >
                                                            {notation.title}
                                                        </SelectItem>
                                                    )
                                                )}
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </div>
                                </FormItem>
                            )}
                        />
                        <FormField
                            name="letterRange"
                            control={control}
                            rules={{ required: '/20' }}
                            render={({ field }) => (
                                <FormItem
                                    className={`${errors.letterRange ? 'mt-2' : 'mt-1'}  space-y-1`}
                                >
                                    <Select
                                        onValueChange={val => {
                                            field.onChange(val)
                                        }}
                                        value={field.value?.toString()}
                                    >
                                        <FormControl className="w-20">
                                            <SelectTrigger>
                                                <SelectValue placeholder="..." />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent className="min-w-fit">
                                            {notations
                                                .find(
                                                    notation =>
                                                        notation.value ===
                                                        watch('scoringType')
                                                )
                                                ?.content.map(val => (
                                                    <SelectItem
                                                        key={val}
                                                        value={val.toString()}
                                                    >
                                                        {val}
                                                    </SelectItem>
                                                ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                    <FormField
                        name="type"
                        control={control}
                        rules={{ required: t('form_type_error') }}
                        render={({ field }) => (
                            <FormItem className="flex items-center space-y-1 w-full gap-4">
                                <FormLabel className={`min-w-40 text-left`}>
                                    {t('form_type')}
                                </FormLabel>
                                <div className="flex-1">
                                    <Select
                                        onValueChange={val => {
                                            field.onChange(val)
                                        }}
                                        value={field.value?.toString()}
                                    >
                                        <FormControl className="flex-1">
                                            <SelectTrigger>
                                                <SelectValue
                                                    placeholder={t(
                                                        'form_type_placeholder'
                                                    )}
                                                />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {typeEvalutations.map(
                                                (typeEval: any, i: any) => (
                                                    <SelectItem
                                                        key={i}
                                                        value={typeEval.value}
                                                    >
                                                        {typeEval.title}
                                                    </SelectItem>
                                                )
                                            )}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </div>
                            </FormItem>
                        )}
                    />
                    <FormField
                        name="themeId"
                        control={control}
                        rules={{ required: t('form_theme_error') }}
                        render={({ field }) => (
                            <FormItem className="  flex items-center space-y-1 w-full gap-4">
                                <FormLabel className={`min-w-40 text-left`}>
                                    {t('form_theme')}
                                </FormLabel>
                                <div className="flex-1">
                                    {!isAddingTheme ? (
                                        <Select
                                            onValueChange={val => {
                                                if (val === 'new') {
                                                    dispatch({
                                                        isAddingTheme: true
                                                    })
                                                } else {
                                                    field.onChange(val)
                                                    dispatch({
                                                        isAddingTheme: false
                                                    })
                                                }
                                            }}
                                            value={field.value!}
                                        >
                                            <FormControl className="flex-1">
                                                <SelectTrigger>
                                                    <SelectValue
                                                        placeholder={t(
                                                            'form_theme_placeholder'
                                                        )}
                                                    />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectItem key={0} value="new">
                                                    {t('form_new_theme')}
                                                </SelectItem>
                                                {themesP.map(theme => (
                                                    <SelectItem
                                                        key={theme.id}
                                                        value={theme.id!}
                                                    >
                                                        {theme.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    ) : (
                                        <FormControl>
                                            <div className="flex items-center gap-1">
                                                <Input
                                                    ref={inputRef}
                                                    placeholder={t(
                                                        'form_new_theme_placeholder'
                                                    )}
                                                    value={newThemeName}
                                                    onChange={e =>
                                                        dispatch({
                                                            newThemeName:
                                                                e.target.value
                                                        })
                                                    }
                                                    onKeyDown={handleKeyDown}
                                                />
                                                <Button
                                                    variant="ghost"
                                                    className="text-dinoBotRed hover:text-dinoBotRed/80 px-1"
                                                    onClick={cancelAddTheme}
                                                >
                                                    <X />
                                                </Button>
                                            </div>
                                        </FormControl>
                                    )}
                                    <FormMessage />
                                </div>
                            </FormItem>
                        )}
                    />
                    <Separator className="w-44" />
                    <p className="text-dinoBotGray/90">
                        {t('form_param_view')}
                    </p>
                    <FormField
                        name="fontName"
                        control={control}
                        rules={{ required: t('form_police_error') }}
                        render={({ field }) => (
                            <FormItem className="  flex items-center space-y-1 w-full gap-4">
                                <FormLabel className={`min-w-40 text-left`}>
                                    {t('form_police')}
                                </FormLabel>
                                <div className="flex-1">
                                    <Select
                                        onValueChange={val => {
                                            field.onChange(val)
                                        }}
                                        value={field.value!}
                                    >
                                        <FormControl className="flex-1">
                                            <SelectTrigger>
                                                <SelectValue
                                                    placeholder={t(
                                                        'form_police_placeholder'
                                                    )}
                                                />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {googleFonts.map(font => (
                                                <SelectItem
                                                    key={font}
                                                    value={font}
                                                    style={{ fontFamily: font }}
                                                >
                                                    {font}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </div>
                            </FormItem>
                        )}
                    />
                    <FormField
                        name="fontSize"
                        control={control}
                        rules={{ required: t('form_size_error') }}
                        render={({ field }) => (
                            <FormItem className="  flex items-center space-y-1 w-full gap-4">
                                <FormLabel className={`min-w-40 text-left`}>
                                    {t('form_size')}
                                </FormLabel>
                                <div className="flex-1">
                                    <Select
                                        onValueChange={val => {
                                            field.onChange(Number(val))
                                        }}
                                        value={field.value?.toString()}
                                    >
                                        <FormControl className="flex-1">
                                            <SelectTrigger>
                                                <SelectValue
                                                    placeholder={t(
                                                        'form_size_placeholder'
                                                    )}
                                                />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {fontSizes.map(size => (
                                                <SelectItem
                                                    key={size}
                                                    value={size.toString()}
                                                >
                                                    {size} px
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </div>
                            </FormItem>
                        )}
                    />
                    <div className="flex justify-center mt-4 w-full">
                        <Button
                            type="submit"
                            className="bg-dinoBotBlue hover:bg-dinoBotVibrantBlue text-dinoBotWhite"
                            disabled={isOnSubmit}
                        >
                            {isOnSubmit ? (
                                <>
                                    <Loader2 className="mr-2 size-4 animate-spin" />
                                    {t('submit_loading')}
                                </>
                            ) : isUpdate ? (
                                t('submit_update')
                            ) : (
                                t('submit_next')
                            )}
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    )
}

export default ParamEvaluation

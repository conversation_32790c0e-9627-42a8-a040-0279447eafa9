'use client'

import { logOut } from '@/lib/user/log'
import { useEffect } from 'react'

/**
 * This component forces a user logout by calling signOut on mount.
 * It's used when a user's session is valid, but the user has been deleted from the database.
 */
export default function ForceLogout() {
    useEffect(() => {
        // Redirect to login page after logout
        ;(async () => {
            await logOut()
        })()
    }, [])

    // You can optionally render a loading spinner or a message here
    return null
}

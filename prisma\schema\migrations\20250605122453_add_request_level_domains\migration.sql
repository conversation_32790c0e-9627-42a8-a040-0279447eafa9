-- CreateEnum
CREATE TYPE "RequestStatus" AS ENUM ('PENDING', 'ACCEPTED', 'REJECTED', 'CANCELED', 'EXPIRED', 'DELETED');

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "max_domains" INTEGER NOT NULL DEFAULT 2,
ADD COLUMN     "max_level_domains" INTEGER NOT NULL DEFAULT 4;

-- CreateTable
CREATE TABLE "requests_level_domain" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "user_id" TEXT NOT NULL,
    "explanation" TEXT NOT NULL,
    "status" "RequestStatus" NOT NULL DEFAULT 'PENDING',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "requests_level_domain_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "request_level_domain_items" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "request_id" TEXT NOT NULL,
    "level_domain_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "request_level_domain_items_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "request_level_domain_items_request_id_level_domain_id_key" ON "request_level_domain_items"("request_id", "level_domain_id");

-- AddForeignKey
ALTER TABLE "requests_level_domain" ADD CONSTRAINT "requests_level_domain_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "request_level_domain_items" ADD CONSTRAINT "request_level_domain_items_request_id_fkey" FOREIGN KEY ("request_id") REFERENCES "requests_level_domain"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "request_level_domain_items" ADD CONSTRAINT "request_level_domain_items_level_domain_id_fkey" FOREIGN KEY ("level_domain_id") REFERENCES "level_domain"("id") ON DELETE CASCADE ON UPDATE CASCADE;

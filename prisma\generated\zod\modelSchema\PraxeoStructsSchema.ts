import { z } from 'zod';
import { JsonValueSchema } from '../inputTypeSchemas/JsonValueSchema'
import { PartWithRelationsSchema, PartPartialWithRelationsSchema, PartOptionalDefaultsWithRelationsSchema } from './PartSchema'
import type { PartWithRelations, PartPartialWithRelations, PartOptionalDefaultsWithRelations } from './PartSchema'

/////////////////////////////////////////
// PRAXEO STRUCTS SCHEMA
/////////////////////////////////////////

export const PraxeoStructsSchema = z.object({
  id: z.number(),
  contents: JsonValueSchema,
  partId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PraxeoStructs = z.infer<typeof PraxeoStructsSchema>

/////////////////////////////////////////
// PRAXEO STRUCTS PARTIAL SCHEMA
/////////////////////////////////////////

export const PraxeoStructsPartialSchema = PraxeoStructsSchema.partial()

export type PraxeoStructsPartial = z.infer<typeof PraxeoStructsPartialSchema>

/////////////////////////////////////////
// PRAXEO STRUCTS OPTIONAL DEFAULTS SCHEMA
/////////////////////////////////////////

export const PraxeoStructsOptionalDefaultsSchema = PraxeoStructsSchema.merge(z.object({
  id: z.number().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type PraxeoStructsOptionalDefaults = z.infer<typeof PraxeoStructsOptionalDefaultsSchema>

/////////////////////////////////////////
// PRAXEO STRUCTS RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoStructsRelations = {
  part: PartWithRelations;
};

export type PraxeoStructsWithRelations = z.infer<typeof PraxeoStructsSchema> & PraxeoStructsRelations

export const PraxeoStructsWithRelationsSchema: z.ZodType<PraxeoStructsWithRelations> = PraxeoStructsSchema.merge(z.object({
  part: z.lazy(() => PartWithRelationsSchema),
}))

/////////////////////////////////////////
// PRAXEO STRUCTS OPTIONAL DEFAULTS RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoStructsOptionalDefaultsRelations = {
  part: PartOptionalDefaultsWithRelations;
};

export type PraxeoStructsOptionalDefaultsWithRelations = z.infer<typeof PraxeoStructsOptionalDefaultsSchema> & PraxeoStructsOptionalDefaultsRelations

export const PraxeoStructsOptionalDefaultsWithRelationsSchema: z.ZodType<PraxeoStructsOptionalDefaultsWithRelations> = PraxeoStructsOptionalDefaultsSchema.merge(z.object({
  part: z.lazy(() => PartOptionalDefaultsWithRelationsSchema),
}))

/////////////////////////////////////////
// PRAXEO STRUCTS PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoStructsPartialRelations = {
  part?: PartPartialWithRelations;
};

export type PraxeoStructsPartialWithRelations = z.infer<typeof PraxeoStructsPartialSchema> & PraxeoStructsPartialRelations

export const PraxeoStructsPartialWithRelationsSchema: z.ZodType<PraxeoStructsPartialWithRelations> = PraxeoStructsPartialSchema.merge(z.object({
  part: z.lazy(() => PartPartialWithRelationsSchema),
})).partial()

export type PraxeoStructsOptionalDefaultsWithPartialRelations = z.infer<typeof PraxeoStructsOptionalDefaultsSchema> & PraxeoStructsPartialRelations

export const PraxeoStructsOptionalDefaultsWithPartialRelationsSchema: z.ZodType<PraxeoStructsOptionalDefaultsWithPartialRelations> = PraxeoStructsOptionalDefaultsSchema.merge(z.object({
  part: z.lazy(() => PartPartialWithRelationsSchema),
}).partial())

export type PraxeoStructsWithPartialRelations = z.infer<typeof PraxeoStructsSchema> & PraxeoStructsPartialRelations

export const PraxeoStructsWithPartialRelationsSchema: z.ZodType<PraxeoStructsWithPartialRelations> = PraxeoStructsSchema.merge(z.object({
  part: z.lazy(() => PartPartialWithRelationsSchema),
}).partial())

export default PraxeoStructsSchema;

'server-only'
import RequestStatusSchema from '@/prisma/generated/zod/inputTypeSchemas/RequestStatusSchema'
import prisma from '../prisma-client'

export const fetchRequestsLevelDomainByUserID = async (userId: string) => {
    return await prisma.requestsLevelDomain.findMany({
        where: {
            userId
        },
        include: {
            requestLevelDomains: {
                include: {
                    levelDomain: {
                        include: {
                            domain: true,
                            level: true
                        }
                    }
                }
            }
        },
        orderBy: {
            createdAt: 'desc'
        }
    })
}

export const addRequestLevelDomain = async (
    userId: string,
    explanation: string,
    levelDomainIds: string[]
) => {
    return await prisma.requestsLevelDomain.create({
        data: {
            userId,
            explanation,
            status: RequestStatusSchema.Enum.PENDING,
            requestLevelDomains: {
                createMany: {
                    data: levelDomainIds.map(levelDomainId => ({
                        levelDomainId
                    }))
                }
            }
        }
    })
}

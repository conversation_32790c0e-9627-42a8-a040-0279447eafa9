-- CreateTable
CREATE TABLE "tm_skills" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "description" TEXT,
    "part_id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "tm_skills_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "tm_skills" ADD CONSTRAINT "tm_skills_part_id_fkey" FOREIGN KEY ("part_id") REFERENCES "tm_parts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

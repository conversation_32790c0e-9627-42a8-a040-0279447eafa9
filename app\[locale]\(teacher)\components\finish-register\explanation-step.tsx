import { useTranslations } from 'next-intl'
import React from 'react'
import { selectRegisterStepsStore } from '../../store/register-steps.store'
import { Textarea } from '@/components/ui/textarea'
import { cn } from '@/lib/utils/utils'

const ExplanationStep = () => {
    const t = useTranslations(`teacher.register`)
    const explanation = selectRegisterStepsStore.use.explanation()
    const { setExplanation } = selectRegisterStepsStore.use.actions()
    return (
        <div className="flex flex-col gap-3">
            <h2 className={cn('text-dinoBotBlue')}>{t('step_3_explain')}</h2>
            <Textarea
                value={explanation}
                onChange={e => setExplanation(e.target.value)}
                placeholder={t('explanation_placeholder')}
            />
        </div>
    )
}

export default ExplanationStep

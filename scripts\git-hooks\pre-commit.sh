#!/bin/sh
echo "Running pre-commit hooks on staged files..."

# Get all staged .ts, .tsx, and .mdx files for Prettier, excluding prisma folder
ALL_STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(ts|tsx|mdx)$' | grep -v '^prisma/')

if [ -z "$ALL_STAGED_FILES" ]; then
  echo "No staged .ts, .tsx, or .mdx files to check. Skipping pre-commit hooks."
  exit 0
fi

echo "Staged files for Prettier: $ALL_STAGED_FILES"

# Run Prettier check on all staged files
echo "Running Prettier check..."

# pnpm run format:check -- $ALL_STAGED_FILES
pnpm run format:write -- $ALL_STAGED_FILES
# Stage the formatted files
git add $ALL_STAGED_FILES
# if [ $? -ne 0 ]; then
#   echo "Prettier check failed. Please run 'pnpm run format:write' to fix formatting issues."
#   exit 1
# fi

# Check if there are any staged files relevant for ESLint (within app, lib, or components)
# If there are, run `pnpm run lint` without arguments to let Next.js handle file discovery.
# ESLINT_RELEVANT_STAGED_FILES=$(echo "$ALL_STAGED_FILES" | grep -E '^(app|lib|components)/')
# if [ -n "$ESLINT_RELEVANT_STAGED_FILES" ]; then
#   echo "Running ESLint on relevant project files..."
#   # Run ESLint without passing specific file paths, letting `next lint` discover files.
#   pnpm run lint
#   if [ $? -ne 0 ]; then
#     echo "ESLint failed. Please fix linting errors."
#     exit 1
#   fi
# else
#   echo "No relevant staged files in app, lib, or components for ESLint. Skipping ESLint check."
# fi
#
echo "Pre-commit hooks passed."


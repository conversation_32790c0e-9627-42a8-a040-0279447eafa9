import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import React, { useEffect, useState } from 'react'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'

import InfoTooltip from '@/components/ui/info-tooltip'
import { Chapter, Part } from '@/lib/training-mode/types'
import { Input } from '@/components/ui/input'
import { Slider } from '@/components/ui/slider'
import useExoModeStore, {
    selectUseExoModeStore
} from '@/lib/stores/exercise-mode-store/exercise-store'
import { toast } from 'sonner'
import ErrorTooltip from '@/components/ui/error-tooltip'
import { useRouter } from '@/i18n/routing'
import { useLocale, useTranslations } from 'next-intl'
import { getLangProps } from '@/lib/utils/string.utils'

interface ExoFromDbProps {
    chapters: Chapter[]
    getParts: (chapterId: string) => Promise<Part[]>
}

function ExoFromDb({ chapters, getParts }: ExoFromDbProps) {
    const formData = selectUseExoModeStore.use.exoInfoFromDb()
    const [selectedChapter, setSelectedChapter] = useState<string | undefined>(
        formData.chapterId
    )
    const [parts, setParts] = useState<Part[]>([])
    const handleFormChange = selectUseExoModeStore.use.updateExoInfoFromDb()
    const [showError, setShowError] = useState<boolean>(false)
    const setMode = useExoModeStore(state => state.setMode)
    const t = useTranslations('app.mode.exo.tab.db')
    const router = useRouter()
    const lang = useLocale()
    const locale = useLocale()
    useEffect(() => {
        setMode('FROM_DB')
    }, [])

    useEffect(() => {
        if (formData.chapterId) {
            setSelectedChapter(formData.chapterId)
            ;(async () => {
                const parts = await getParts(formData.chapterId)
                setParts(parts)
            })()
        } else {
            setSelectedChapter(undefined)
            setParts([])
        }
    }, [formData.chapterId])

    useEffect(() => {
        if (selectedChapter) {
            handleFormChange('chapterId', selectedChapter)
        }
    }, [selectedChapter])

    const handleQuestionNumberChange = (value: number) => {
        try {
            // const int = parseInt(value)
            if (value > 10) {
                handleFormChange('qstNbr', 10)
                toast.info(t('tinfo.n10'), { duration: 2500 })
            } else if (value < 1) {
                handleFormChange('qstNbr', 1)
                toast.info(t('tinfo.n1'), { duration: 2500 })
            } else {
                handleFormChange('qstNbr', value)
            }
        } catch (error) {
            console.log(error)
            handleFormChange('qstNbr', 5)
        }
    }

    useEffect(() => {
        if (formData.qstNbr) handleQuestionNumberChange(formData.qstNbr)
    }, [formData.qstNbr])

    const submit = () => {
        if (formData.chapterId && formData.partId) {
            if (formData.qstNbr) {
                setShowError(false)
                setMode('FROM_DB')
                router.push('/train')
            } else {
                toast.error(t('tinfo.error'))
            }
        } else {
            setShowError(true)
            toast.info(t('tinfo.info'))
        }
    }

    useEffect(() => {
        console.log('selcted chapter: ', selectedChapter)
    }, [selectedChapter])

    return (
        <div className="flex flex-col gap-4 w-full">
            <div className="flex flex-col gap-y-9">
                <div className="flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('chois.title')}{' '}
                    </div>
                    <Select
                        value={formData.chapterId}
                        onValueChange={value => {
                            handleFormChange('partId', '')
                            setSelectedChapter(value)
                        }}
                    >
                        <SelectTrigger className="max-w-full">
                            <SelectValue placeholder={t('chois.placeholder')} />
                        </SelectTrigger>
                        <SelectContent className="h-72">
                            <SelectGroup>
                                {chapters.map(chapter => (
                                    <SelectItem
                                        key={chapter.id}
                                        value={chapter.id}
                                    >
                                        {getLangProps({
                                            obj: chapter,
                                            base: 'title',
                                            lang
                                        })}
                                    </SelectItem>
                                ))}
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('part.title')}{' '}
                    </div>
                    <Select
                        disabled={parts.length <= 0}
                        value={formData.partId}
                        onValueChange={value =>
                            handleFormChange('partId', value)
                        }
                    >
                        <SelectTrigger className="max-w-full">
                            <SelectValue placeholder={t('part.placeholder')} />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                {parts.map(part => (
                                    <SelectItem key={part.id} value={part.id}>
                                        {getLangProps({
                                            obj: part,
                                            base: 'name',
                                            lang
                                        })}
                                    </SelectItem>
                                ))}
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>

                <div className="w-full flex flex-row gap-2">
                    <div className="w-1/3 flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('questions-lenght')}
                    </div>
                    <Input
                        type="number"
                        value={formData.qstNbr}
                        min={1}
                        max={10}
                        className="h-9 rounded-md"
                        onChange={e =>
                            handleFormChange('qstNbr', e.target.value)
                        }
                    />
                </div>

                <div className="w-full sm:w-4/5 md:w-3/5">
                    <div className="flex gap-5 items-center text-sm font-bold text-dinoBotDarkGray">
                        <span>{t('difficulty')}</span>
                        <div className="flex-1">
                            <Slider
                                defaultValue={[formData.difficulty]}
                                min={0}
                                max={3}
                                step={1}
                                className="w-full"
                                onValueChange={value =>
                                    handleFormChange('difficulty', value[0])
                                }
                            />
                        </div>
                        <span>{formData.difficulty}/3</span>
                    </div>
                </div>

                <div className="flex flex-col gap-1">
                    <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {' '}
                        {t('custom-prompt')}{' '}
                        <span className="font-normal">{t('opt')}</span>{' '}
                    </div>
                    <div className="mt-2">
                        <Textarea
                            placeholder={t('redigez')}
                            value={formData.customPrompt}
                            onChange={e => {
                                if (e.target.value.length <= 1000)
                                    handleFormChange(
                                        'customPrompt',
                                        e.target.value
                                    )
                            }}
                        ></Textarea>
                        <p className="text-xs text-right mt-1 text-dinoBotGray">
                            {formData.customPrompt.length ?? 0}/1000{' '}
                            {t('caracteres')}
                        </p>
                    </div>
                </div>
            </div>
            <div className="w-full flex justify-center items-center mt-2">
                <Button
                    className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
                    onClick={submit}
                >
                    {t('submit')}
                </Button>
            </div>
        </div>
    )
}

export default ExoFromDb

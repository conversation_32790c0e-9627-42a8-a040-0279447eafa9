"use client"

import { ClassSSEPayload } from "@/lib/sse/class/schema";

export async function classEmit<T>(payload: ClassSSEPayload<T>): Promise<void> {
    const validPayload = ClassSSEPayload.parse(payload);
    try {
        const res = await fetch(`/api/sse/class/emit`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(validPayload)
        });

        if (!res.ok) {
            console.error(`Failed to emit event to class ${validPayload.classIds} : ${await res.text()}`);
        }
    } catch (err) {
        console.error(`Error emitting event to class ${validPayload.classIds} : ${err}`);
    }
}

export function classListen<T>(
    classId: string,
    event: "evaluations" | "events",
    callback: (payload: ClassSSEPayload<T>) => void | Promise<void>,
): EventSource {
    const url = `/api/sse/class/listen/${classId}?event=${encodeURIComponent(event)}`;
    const source = new EventSource(url);

    source.addEventListener(event, (e) => {
        try {
            const rawData = JSON.parse((e as MessageEvent).data);
            const data = ClassSSEPayload.parse(rawData) as ClassSSEPayload<T>;
            callback(data);
        } catch (err) {
            console.error(`Error parsing SSE message for event "${event}": ${err}`);
        }
    });

    source.onopen = () => {
        console.log(`SSE connected to class ${classId} event "${event}"`);
    };

    source.onerror = (err) => {
        console.error(`SSE error on class ${classId} event "${event}": ${err}`);
    };

    return source;
}

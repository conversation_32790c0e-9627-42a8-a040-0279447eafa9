import { ClassSSEPayload } from "@/lib/sse/class/schema";
import { logger } from "@/logger/logger";

export async function classEmit<T>(payload: ClassSSEPayload<T>): Promise<void> {
    const validPayload = ClassSSEPayload.parse(payload);
    try {
        const res = await fetch(`/api/sse/class/emit`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(validPayload)
        });

        if (!res.ok) {
            logger.error(`Failed to emit event to class ${validPayload.classIds} : ${await res.text()}`);
        }
    } catch (err) {
        logger.error(`Error emitting event to class ${validPayload.classIds} : ${err}`);
    }
}

export function classListen<T>(
    classId: string,
    event: "evaluations" | "events",
    callback: (payload: ClassSSEPayload<T>) => void | Promise<void>,
): EventSource {
    const url = `/api/sse/class/listen/${classId}?event=${encodeURIComponent(event)}`;
    const source = new EventSource(url);

    source.addEventListener(event, (e) => {
        try {
            const rawData = JSON.parse((e as MessageEvent).data);
            const data = ClassSSEPayload.parse(rawData) as ClassSSEPayload<T>;
            callback(data);
        } catch (err) {
            logger.error(`Error parsing SSE message for event "${event}": ${err}`);
        }
    });

    source.onopen = () => {
        logger.info(`SSE connected to class ${classId} event "${event}"`);
    };

    source.onerror = (err) => {
        logger.error(`SSE error on class ${classId} event "${event}": ${err}`);
    };

    return source;
}

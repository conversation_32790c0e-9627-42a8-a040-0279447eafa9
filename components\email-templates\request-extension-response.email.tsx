import RequestStatusSchema from '@/prisma/generated/zod/inputTypeSchemas/RequestStatusSchema'
import {
    Body,
    But<PERSON>,
    Column,
    Container,
    Head,
    Heading,
    Html,
    Img,
    Link,
    Preview,
    Row,
    Section,
    Tailwind,
    Text
} from '@react-email/components'
import { getLocale, getTranslations } from 'next-intl/server'
import { getLangDir } from 'rtl-detect'
import { z } from 'zod'

type RequestExtensionResponseEmailProps = {
    firstName: string
    lastName: string
    status: z.infer<typeof RequestStatusSchema>
    levelsDomain: string[]
    requestDate: string
    adminComment?: string
}

export default async function RequestExtensionResponseEmail({
    firstName,
    lastName,
    status,
    levelsDomain,
    requestDate,
    adminComment
}: RequestExtensionResponseEmailProps) {
    const t = await getTranslations('email')
    const locale = await getLocale()
    const direction = getLangDir(locale)

    const baseUrl = process.env.BASE_URL || 'http://localhost:3000'
    const legalNoticeUrl =
        process.env.EMAIL_LEGAL_NOTICE_URL ||
        'https://dinobot.fr/mentions-legales'
    const dataProtectionUrl =
        process.env.EMAIL_DATA_PROTECTION_URL ||
        'https://dinobot.fr/protection-donnees'
    const instagramUrl =
        process.env.EMAIL_INSTAGRAM_URL || 'https://instagram.com/dinobot'
    const linkedinUrl =
        process.env.EMAIL_LINKEDIN_URL || 'https://linkedin.com/company/dinobot'
    const facebookUrl =
        process.env.EMAIL_FACEBOOK_URL || 'https://facebook.com/dinobot'
    const youtubeUrl =
        process.env.EMAIL_YOUTUBE_URL || 'https://youtube.com/dinobot'

    const isApproved = status === 'ACCEPTED'

    return (
        <Html dir={direction}>
            <Head />
            <Preview>{t(`requestExtensionResponse.${status}.preview`)}</Preview>
            <Tailwind>
                <Body className="bg-white font-sans">
                    <Container className="mx-auto p-0 max-w-[600px]">
                        {/* Blue header with greeting */}
                        <Section className="bg-blue-600 p-5 text-center text-white">
                            <Heading className="text-3xl font-bold my-2.5 text-white">
                                {t('requestExtensionResponse.greeting', {
                                    prenom: firstName,
                                    nom: lastName
                                })}
                            </Heading>
                        </Section>

                        {/* Main content */}
                        <Section className="p-5">
                            <Text className="text-base leading-6 text-gray-800 my-5">
                                {t(
                                    `requestExtensionResponse.${status}.mainTitle`
                                )}
                            </Text>

                            <Text className="text-lg font-bold leading-6 text-gray-800 my-5">
                                {t('requestExtensionResponse.detailsTitle')}
                            </Text>

                            {/* Request details */}
                            <Section className="my-5 bg-gray-50 p-4 rounded-lg">
                                {/* Levels domain */}
                                <Row className="mb-3">
                                    <Column className="align-baseline w-[220px]">
                                        <Text className="text-sm font-bold m-0 text-gray-700">
                                            {t(
                                                'requestExtensionResponse.subjectsLabel'
                                            )}
                                        </Text>
                                    </Column>
                                    <Column className="align-middle">
                                        <ul className="list-disc pl-4 m-0">
                                            {levelsDomain.map(
                                                (domain, index) => (
                                                    <li
                                                        key={index}
                                                        className="text-sm text-gray-800 mb-1"
                                                    >
                                                        {domain}
                                                    </li>
                                                )
                                            )}
                                        </ul>
                                    </Column>
                                </Row>

                                {/* Date */}
                                <Row className="mb-3">
                                    <Column className="align-baseline w-[220px]">
                                        <Text className="text-sm font-bold m-0 text-gray-700">
                                            {t(
                                                'requestExtensionResponse.dateLabel'
                                            )}
                                        </Text>
                                    </Column>
                                    <Column className="align-middle">
                                        <Text className="text-sm m-0 text-gray-800">
                                            {requestDate}
                                        </Text>
                                    </Column>
                                </Row>

                                {/* Status */}
                                <Row className="mb-3">
                                    <Column className="align-baseline w-[220px]">
                                        <Text className="text-sm font-bold m-0 text-gray-700">
                                            {t(
                                                'requestExtensionResponse.statusLabel'
                                            )}
                                        </Text>
                                    </Column>
                                    <Column className="align-middle">
                                        <Text
                                            className={`text-sm font-bold m-0 px-3 py-1 rounded-full inline-block ${
                                                isApproved
                                                    ? 'bg-green-100 text-green-600'
                                                    : 'bg-red-100 text-red-600'
                                            }`}
                                        >
                                            {t(
                                                `requestExtensionResponse.${status}.statusLabel`
                                            )}
                                        </Text>
                                    </Column>
                                </Row>
                            </Section>

                            {/* Admin comment - only show if adminComment exists */}
                            {adminComment && (
                                <>
                                    <Text className="text-base font-bold leading-6 text-gray-800 my-5">
                                        {t(
                                            `requestExtensionResponse.${status}.commentLabel`
                                        )}
                                    </Text>
                                    <Section className="my-5 bg-blue-50 p-4 rounded-lg border-l-4 border-blue-600">
                                        <Text className="text-base italic m-0 text-gray-700">
                                            "{adminComment}"
                                        </Text>
                                    </Section>
                                </>
                            )}

                            <Text className="text-base leading-6 text-gray-800 my-5">
                                {t(
                                    `requestExtensionResponse.${status}.bottomText`
                                )}
                            </Text>

                            {/* Benefits section (only for approved) */}
                            {isApproved && (
                                <Section className="my-5">
                                    {t
                                        .raw(
                                            `requestExtensionResponse.ACCEPTED.benefits`
                                        )
                                        .map(
                                            (
                                                benefit: string,
                                                index: number
                                            ) => (
                                                <Row
                                                    key={index}
                                                    className={
                                                        index > 0 ? 'mt-3' : ''
                                                    }
                                                >
                                                    <Column className="align-baseline w-[24px]">
                                                        <Text className="m-0 text-[16px] text-blue-600">
                                                            •
                                                        </Text>
                                                    </Column>
                                                    <Column className="align-middle pl-3">
                                                        <Text className="text-base m-0 text-gray-800">
                                                            {benefit}
                                                        </Text>
                                                    </Column>
                                                </Row>
                                            )
                                        )}
                                </Section>
                            )}

                            {/* CTA Button */}
                            <Section className="text-center my-7">
                                <Button
                                    className="bg-blue-600 rounded-full text-white text-base font-bold no-underline inline-block px-5 py-3"
                                    href={`${baseUrl}/teacher/dashboard`}
                                >
                                    {t('requestExtensionResponse.buttonText')}
                                </Button>
                            </Section>

                            <Text className="text-base leading-6 text-gray-800 my-5">
                                {t('requestExtensionResponse.finalText')}
                            </Text>

                            <Text className="text-base leading-6 text-gray-800 my-5">
                                {t('requestExtensionResponse.signature')}
                            </Text>

                            <Text className="text-base italic my-5 text-gray-800">
                                -{t('requestExtensionResponse.team')}
                            </Text>

                            {/* Logo */}
                            <Section className="text-center my-7 w-full flex item-center justify-center">
                                <Img
                                    src={`${baseUrl}/dinobot-logo-small.svg`}
                                    width="60"
                                    height="60"
                                    alt="Logo Dinobot"
                                />
                            </Section>
                        </Section>

                        {/* Social media footer */}
                        <Section className="bg-blue-600 p-5 text-center text-white">
                            <Text className="text-base font-bold mb-4 text-white">
                                {t('common.followUs')}
                            </Text>
                            <Row className="w-60 mx-auto">
                                <Column className="w-15 text-center">
                                    <Link href={instagramUrl}>
                                        <Img
                                            src={`${baseUrl}/instagram.png`}
                                            width="32"
                                            height="32"
                                            alt="Instagram"
                                        />
                                    </Link>
                                </Column>
                                <Column className="w-15 text-center">
                                    <Link href={linkedinUrl}>
                                        <Img
                                            src={`${baseUrl}/linkedin.png`}
                                            width="32"
                                            height="32"
                                            alt="LinkedIn"
                                        />
                                    </Link>
                                </Column>
                                <Column className="w-15 text-center">
                                    <Link href={facebookUrl}>
                                        <Img
                                            src={`${baseUrl}/facebook.png`}
                                            width="32"
                                            height="32"
                                            alt="Facebook"
                                        />
                                    </Link>
                                </Column>
                                <Column className="w-15 text-center">
                                    <Link href={youtubeUrl}>
                                        <Img
                                            src={`${baseUrl}/youtube.png`}
                                            width="32"
                                            height="32"
                                            alt="YouTube"
                                        />
                                    </Link>
                                </Column>
                            </Row>
                        </Section>

                        {/* Legal footer */}
                        <Section className="p-5 text-center bg-gray-100">
                            <Text className="text-base font-bold m-0 text-gray-800">
                                {t('common.company')}
                            </Text>
                            <Text className="text-sm my-1 mb-5 text-gray-600">
                                {t('common.address')}
                            </Text>

                            <Section className="my-2.5">
                                <Link
                                    href={legalNoticeUrl}
                                    className="text-blue-600 no-underline"
                                >
                                    {t('common.legalNotice')}
                                </Link>{' '}
                                |{' '}
                                <Link
                                    href={dataProtectionUrl}
                                    className="text-blue-600 no-underline"
                                >
                                    {t('common.dataProtection')}
                                </Link>
                            </Section>

                            <Text className="text-xs text-gray-500 my-5 mt-1">
                                {t('common.automaticEmail')}
                            </Text>
                        </Section>
                    </Container>
                </Body>
            </Tailwind>
        </Html>
    )
}

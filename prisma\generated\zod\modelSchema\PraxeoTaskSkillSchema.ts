import { z } from 'zod';
import { PraxeoTaskWithRelationsSchema, PraxeoTaskPartialWithRelationsSchema, PraxeoTaskOptionalDefaultsWithRelationsSchema } from './PraxeoTaskSchema'
import type { PraxeoTaskWithRelations, PraxeoTaskPartialWithRelations, PraxeoTaskOptionalDefaultsWithRelations } from './PraxeoTaskSchema'
import { TmSkillsWithRelationsSchema, TmSkillsPartialWithRelationsSchema, TmSkillsOptionalDefaultsWithRelationsSchema } from './TmSkillsSchema'
import type { TmSkillsWithRelations, TmSkillsPartialWithRelations, TmSkillsOptionalDefaultsWithRelations } from './TmSkillsSchema'

/////////////////////////////////////////
// PRAXEO TASK SKILL SCHEMA
/////////////////////////////////////////

export const PraxeoTaskSkillSchema = z.object({
  id: z.number(),
  praxeoTaskId: z.number(),
  tmSkillId: z.string(),
})

export type PraxeoTaskSkill = z.infer<typeof PraxeoTaskSkillSchema>

/////////////////////////////////////////
// PRAXEO TASK SKILL PARTIAL SCHEMA
/////////////////////////////////////////

export const PraxeoTaskSkillPartialSchema = PraxeoTaskSkillSchema.partial()

export type PraxeoTaskSkillPartial = z.infer<typeof PraxeoTaskSkillPartialSchema>

/////////////////////////////////////////
// PRAXEO TASK SKILL OPTIONAL DEFAULTS SCHEMA
/////////////////////////////////////////

export const PraxeoTaskSkillOptionalDefaultsSchema = PraxeoTaskSkillSchema.merge(z.object({
  id: z.number().optional(),
}))

export type PraxeoTaskSkillOptionalDefaults = z.infer<typeof PraxeoTaskSkillOptionalDefaultsSchema>

/////////////////////////////////////////
// PRAXEO TASK SKILL RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoTaskSkillRelations = {
  praxeoTask: PraxeoTaskWithRelations;
  tmSkill: TmSkillsWithRelations;
};

export type PraxeoTaskSkillWithRelations = z.infer<typeof PraxeoTaskSkillSchema> & PraxeoTaskSkillRelations

export const PraxeoTaskSkillWithRelationsSchema: z.ZodType<PraxeoTaskSkillWithRelations> = PraxeoTaskSkillSchema.merge(z.object({
  praxeoTask: z.lazy(() => PraxeoTaskWithRelationsSchema),
  tmSkill: z.lazy(() => TmSkillsWithRelationsSchema),
}))

/////////////////////////////////////////
// PRAXEO TASK SKILL OPTIONAL DEFAULTS RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoTaskSkillOptionalDefaultsRelations = {
  praxeoTask: PraxeoTaskOptionalDefaultsWithRelations;
  tmSkill: TmSkillsOptionalDefaultsWithRelations;
};

export type PraxeoTaskSkillOptionalDefaultsWithRelations = z.infer<typeof PraxeoTaskSkillOptionalDefaultsSchema> & PraxeoTaskSkillOptionalDefaultsRelations

export const PraxeoTaskSkillOptionalDefaultsWithRelationsSchema: z.ZodType<PraxeoTaskSkillOptionalDefaultsWithRelations> = PraxeoTaskSkillOptionalDefaultsSchema.merge(z.object({
  praxeoTask: z.lazy(() => PraxeoTaskOptionalDefaultsWithRelationsSchema),
  tmSkill: z.lazy(() => TmSkillsOptionalDefaultsWithRelationsSchema),
}))

/////////////////////////////////////////
// PRAXEO TASK SKILL PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoTaskSkillPartialRelations = {
  praxeoTask?: PraxeoTaskPartialWithRelations;
  tmSkill?: TmSkillsPartialWithRelations;
};

export type PraxeoTaskSkillPartialWithRelations = z.infer<typeof PraxeoTaskSkillPartialSchema> & PraxeoTaskSkillPartialRelations

export const PraxeoTaskSkillPartialWithRelationsSchema: z.ZodType<PraxeoTaskSkillPartialWithRelations> = PraxeoTaskSkillPartialSchema.merge(z.object({
  praxeoTask: z.lazy(() => PraxeoTaskPartialWithRelationsSchema),
  tmSkill: z.lazy(() => TmSkillsPartialWithRelationsSchema),
})).partial()

export type PraxeoTaskSkillOptionalDefaultsWithPartialRelations = z.infer<typeof PraxeoTaskSkillOptionalDefaultsSchema> & PraxeoTaskSkillPartialRelations

export const PraxeoTaskSkillOptionalDefaultsWithPartialRelationsSchema: z.ZodType<PraxeoTaskSkillOptionalDefaultsWithPartialRelations> = PraxeoTaskSkillOptionalDefaultsSchema.merge(z.object({
  praxeoTask: z.lazy(() => PraxeoTaskPartialWithRelationsSchema),
  tmSkill: z.lazy(() => TmSkillsPartialWithRelationsSchema),
}).partial())

export type PraxeoTaskSkillWithPartialRelations = z.infer<typeof PraxeoTaskSkillSchema> & PraxeoTaskSkillPartialRelations

export const PraxeoTaskSkillWithPartialRelationsSchema: z.ZodType<PraxeoTaskSkillWithPartialRelations> = PraxeoTaskSkillSchema.merge(z.object({
  praxeoTask: z.lazy(() => PraxeoTaskPartialWithRelationsSchema),
  tmSkill: z.lazy(() => TmSkillsPartialWithRelationsSchema),
}).partial())

export default PraxeoTaskSkillSchema;

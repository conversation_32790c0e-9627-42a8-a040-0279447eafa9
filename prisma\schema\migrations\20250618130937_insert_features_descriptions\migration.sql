-- Seed feature flags with descriptions and initial states
INSERT INTO "feature_flags" ("feature_name", "is_enabled", "description", "created_at", "updated_at") VALUES
('STUDENT_CLASSES_VIEW', true, 'Permet aux élèves d''accéder à la gestion de leurs classes, consulter les emplois du temps et les informations relatives à leurs cours. Impact : Les élèves peuvent organiser et suivre leurs différentes matières depuis leur tableau de bord personnel.', NOW(), NOW()),
('STUDENT_CHAT_MODE', true, 'Chatbot par matière pour aider les élèves avec les concepts et exercices. Assistance pédagogique interactive et personnalisée. Impact : Les élèves bénéficient d''une aide instantanée pour comprendre les concepts difficiles et résoudre leurs exercices.', NOW(), NOW()),
('STUDENT_EXERCISE_MODE', true, 'Génération d''exercices contextuels via RAG (Retrieval-Augmented Generation) adaptés au niveau et au programme de chaque élève. Impact : Les élèves peuvent s''entraîner avec des exercices personnalisés générés automatiquement selon leurs besoins.', NOW(), NOW()),
('STUDENT_EVALUATION_MODE', true, 'Génération et passage de contrôles automatisés avec correction instantanée et feedback détaillé pour chaque réponse. Impact : Les élèves peuvent passer des évaluations formatives et obtenir un retour immédiat sur leurs performances.', NOW(), NOW()),
('STUDENT_EXAM_MODE', true, 'Accès aux examens passés avec assistance chatbot pour la révision et la préparation aux futures épreuves. Impact : Les élèves peuvent réviser efficacement en consultant leurs anciens examens avec une aide contextuelle.', NOW(), NOW())
ON CONFLICT ("feature_name") DO UPDATE SET
  "description" = EXCLUDED."description",
  "is_enabled" = EXCLUDED."is_enabled",
  "updated_at" = NOW();
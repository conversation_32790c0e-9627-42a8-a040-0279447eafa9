/*
  Warnings:

  - The values [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>] on the enum `ProvidersType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "ProvidersType_new" AS ENUM ('O<PERSON><PERSON><PERSON><PERSON>', 'ANTHRO<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'WHIS<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', 'DEEPSEEK');
ALTER TYPE "ProvidersType" RENAME TO "ProvidersType_old";
ALTER TYPE "ProvidersType_new" RENAME TO "ProvidersType";
DROP TYPE "ProvidersType_old";
COMMIT;

import { logger } from '@/logger/logger'
import { UnauthorizedError } from '../utils/errors'
import { redirect } from '@/i18n/routing'
import { getLocale } from 'next-intl/server'

type DefaultErrorResponse = {
    error: string
    status: number
}

export function withErrorHandling<Args extends unknown[], Return>(
    fn: (...args: Args) => Promise<Return> | Return
): (...args: Args) => Promise<Return | null | DefaultErrorResponse>

export function withErrorHandling<Args extends unknown[], Return, Errorhandler>(
    fn: (...args: Args) => Promise<Return> | Return,
    onError: (error: Error) => Promise<Errorhandler> | Errorhandler
): (...args: Args) => Promise<Return | null | Errorhandler>

export function withErrorHandling<
    Args extends unknown[],
    Return,
    Errorhandler = DefaultErrorResponse
>(
    fn: (...args: Args) => Promise<Return> | Return,
    onError?: (error: Error) => Promise<Errorhandler> | Errorhandler
) {
    return async (...args: Args) => {
        try {
            fn.name && logger.debug(`Executing function : ${fn.name}`)
            return await fn(...args)
        } catch (error) {
            if (onError) {
                return await onError(error as Error)
            }
            return await errorHandle(error as Error)
        }
    }
}
export const errorHandleLogger = async (error: Error) => {
    logger.error(`Error caught in withErrorHandling: ${error}`)
    logger.error(`type of error : ${typeof error}`)
    if (error instanceof UnauthorizedError) {
        logger.error('error unauthorized error=' + error.message)
    }
}

const errorHandle = async (error: Error): Promise<DefaultErrorResponse> => {
    errorHandleLogger(error)
    if (error instanceof UnauthorizedError) {
        const locale = await getLocale()
        redirect({ href: '/', locale })
        return { error: error.message, status: error.statusCode }
    }
    return { error: error.message, status: 500 }
}

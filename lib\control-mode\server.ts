'server-only'

import prisma from '../prisma-client'

import {
    ControlScoringType,
    ControlStatus,
    EvaluationType,
    StudentSubmission
} from '@prisma/client'
import { ControlList } from './types/control/types'
import { logger } from '@/logger/logger'
import {
    Control,
    ControlPartialWithRelations
} from '@/prisma/generated/zod/modelSchema/ControlSchema'
import StudentSubmissionSchema, {
    StudentSubmissionPartialWithRelations
} from '@/prisma/generated/zod/modelSchema/StudentSubmissionSchema'
import { z } from 'zod'
import { StudentPartialWithRelationsSchema } from '@/prisma/generated/zod/modelSchema/StudentSchema'
import {
    StudentAnswerPartialWithRelations,
    StudentAnswerWithRelations
} from '@/prisma/generated/zod/modelSchema/StudentAnswerSchema'
import { ControlExerciseMediaPartial } from '@/prisma/generated/zod/modelSchema/ControlExerciseMediaSchema'

export async function getControlById(
    id: string
): Promise<ControlPartialWithRelations | null> {
    logger.info(`Getting control with id ${id}`)

    const result = (await prisma.control.findUnique({
        where: { id },
        include: {
            assignedClass: {
                include: {
                    level: true,
                    domain: true
                }
            },
            exercises: {
                include: {
                    medias: true,
                    part: {
                        include: {
                            chapter: {
                                include: {
                                    domain: true,
                                    level: true
                                }
                            }
                        }
                    },
                    questions: {
                        include: {
                            medias: true,
                            answers: true
                        }
                    }
                }
            },
            submissions: true
        }
    })) as ControlPartialWithRelations

    if (!result) {
        logger.error(`Control with id ${id} not found`)
        return null
    }

    return result
}

export async function getControls(params: {
    skip?: number
    take?: number
    authorId?: string
    status?: ControlStatus
    scoringType?: ControlScoringType
}): Promise<ControlList> {
    logger.info(`Getting controls with params ${JSON.stringify(params)}`)

    const { skip = 0, take = 50, authorId, status, scoringType } = params

    const controls = (await prisma.control.findMany({
        skip,
        take,
        where: {
            authorId: authorId ? authorId : undefined,
            status: status ? status : undefined,
            scoringType: scoringType ? scoringType : undefined
        },
        include: {
            exercises: true,
            author: {
                select: {
                    id: true,
                    email: true,
                    firstName: true,
                    lastName: true
                }
            },
            assignedClass: {
                select: {
                    id: true,
                    name: true
                }
            }
        },
        orderBy: {
            createdAt: 'desc'
        }
    })) as ControlList

    if (controls.length === 0) {
        logger.info(`No controls found`)
        return []
    }

    controls.forEach(control => {
        control.exerciseCount = control.exercises?.length || 0
    })

    return controls
}

export const fetchAllMediasFromDbByControlId = async (controlId: string) => {
    const questionsMedias = await prisma.controlQuestionMedia.findMany({
        where: {
            question: {
                controlExercise: {
                    controlId
                }
            }
        },
        select: {
            fileUrl: true
        }
    })

    const exercisesMedias = await prisma.controlExerciseMedia.findMany({
        where: {
            controlExercise: {
                controlId
            }
        },
        select: {
            fileUrl: true
        }
    })
    const medias = [
        ...questionsMedias.map(media => media.fileUrl),
        ...exercisesMedias.map(media => media.fileUrl)
    ]
    return medias
}

export const fetchAllPlannedControlesFromDbByThemeId = async (
    themeId: string
) => {
    const controls = prisma.plannedEvaluation.findMany({
        where: {
            control: {
                themeId
            }
        },
        orderBy: {
            createdAt: 'desc'
        },
        include: {
            control: {
                include: {
                    exercises: {
                        include: {
                            questions: {
                                include: {
                                    medias: true
                                }
                            }
                        }
                    }
                }
            }
        }
    })
    return controls
}

export const fetchPlannedEvaluationAndEvaluationById = async (id: string) => {
    return prisma.plannedEvaluation.findFirst({
        where: {
            id
        },
        include: {
            control: {
                include: {
                    exercises: {
                        include: {
                            medias: true,
                            questions: {
                                include: {
                                    medias: true
                                }
                            }
                        }
                    }
                }
            }
        }
    })
}

export const fetchAllPlannedControlesFromDbByClassIdAndDatePlanned = async (
    classId: string
) => {
    const controls = prisma.plannedEvaluation.findMany({
        where: {
            control: {
                assignedClassId: classId
            }
        },
        orderBy: {
            createdAt: 'desc'
        },
        include: {
            control: true
        }
    })
    return controls
}

export const fetchAllControlesFromDbByThemeId = async (themeId: string) => {
    const controls = prisma.control.findMany({
        where: { themeId },
        orderBy: {
            createdAt: 'desc'
        }
    })
    return controls
}

export async function createControl(
    data: ControlPartialWithRelations
): Promise<ControlPartialWithRelations> {
    //logger.info(`Creating control with data ${JSON.stringify(data)}`);

    const hasSolutions =
        data?.exercises?.every(exercise =>
            exercise?.questions?.some(
                question => question.solution !== undefined
            )
        ) ?? false

    return (await prisma.control.create({
        data: {
            ...(data as Control),
            hasSolution: hasSolutions,
            exercises: {
                create: data?.exercises?.map(exercise => ({
                    ...exercise,
                    title: exercise.title ?? 'Untitled Exercise',
                    medias: {
                        create: exercise?.medias ?? []
                    },
                    questions: {
                        create: exercise?.questions?.map(question => ({
                            ...question,
                            type: question.type ?? 'default',
                            medias: {
                                create: question.medias ?? []
                            },
                            answers: question.answers
                                ? { create: question.answers }
                                : undefined
                        }))
                    }
                }))
            }
        } as Control,
        include: {
            exercises: {
                include: {
                    questions: {
                        include: {
                            medias: true
                        }
                    }
                }
            }
        }
    })) as ControlPartialWithRelations
}

export async function updateControl(
    id: string,
    data: ControlPartialWithRelations
): Promise<ControlPartialWithRelations> {
    //logger.info(`Updating control with id ${id} and data ${JSON.stringify(data)}`);

    return (await prisma.control.update({
        where: { id },
        data: {
            ...(data as Control),
            exercises: {
                deleteMany: {},
                create: data.exercises?.map(
                    ({ partId, controlId, part, control, ...exercise }) => ({
                        ...exercise,
                        title: exercise.title ?? 'Untitled Exercise',
                        medias: {
                            create: exercise.medias ?? []
                        },
                        questions: {
                            create: exercise?.questions?.map(
                                ({ controlExerciseId, ...question }) => ({
                                    ...question,
                                    type: question.type ?? 'default',
                                    medias: {
                                        create: question.medias ?? []
                                    },
                                    answers: question.answers
                                        ? { create: question.answers }
                                        : undefined
                                })
                            )
                        }
                    })
                )
            }
        } as Control,
        include: {
            exercises: {
                include: {
                    questions: {
                        include: {
                            medias: true
                        }
                    }
                }
            }
        }
    })) as ControlPartialWithRelations
}

export async function getStudentSubmissionsByControlId(
    controlId: string
): Promise<StudentSubmission[]> {
    logger.info(`Getting student submissions by control id ${controlId}`)

    return prisma.studentSubmission.findMany({
        where: { controlId },
        include: {
            answers: true
        }
    })
}

export async function getStudentSubmissionsCountByControlIdAndStudentId(
    controlId: string,
    studentId: string
): Promise<number> {
    logger.info(`Getting student submissions by control id ${controlId}`)

    const count = await prisma.studentSubmission.count({
        where: {
            studentId,
            controlId
        }
    })

    return count
}

export const createStudentSubmission = async (
    studentSubmissionWithPartialRelations: StudentSubmissionPartialWithRelations
) => {
    const {
        control,
        student,
        studentId,
        controlId,
        answers,
        plannedEvaluationId,
        ...dataWithoutRelations
    } = studentSubmissionWithPartialRelations

    // Ensure `answers` is a flat array
    const flatAnswers = Array.isArray(answers) ? answers.flat() : []
    return prisma.studentSubmission.create({
        data: {
            ...dataWithoutRelations,
            submissionState: 'SUBMITTED',
            answers: {
                createMany: {
                    data: flatAnswers as StudentAnswerWithRelations[] // Use the flattened array
                }
            },
            plannedEvaluation: {
                connect: { id: plannedEvaluationId! }
            },
            student: {
                connect: { id: studentId! }
            },
            control: {
                connect: { id: controlId! }
            }
        }
    })
}

export async function updateStudentSubmission(
    id: string,
    data: Partial<StudentSubmission>
): Promise<StudentSubmission> {
    logger.info(
        `Updating student submission with id ${id} and data ${JSON.stringify(data)}`
    )

    return prisma.studentSubmission.update({
        where: { id },
        data,
        include: {
            answers: true
        }
    })
}

export async function deleteControl(id: string, userId: string): Promise<void> {
    logger.info(`Deleting control with id ${id}`)

    // Check if user is the author of the control
    const control = await prisma.control.findUnique({
        where: { id },
        select: {
            id: true,
            authorId: true
        }
    })

    if (control?.authorId !== userId) {
        throw new Error('User is not the author of the control')
    }

    await prisma.control.delete({
        where: { id: control.id }
    })
}

export async function deleteControls(
    ids: string[],
    userId: string
): Promise<{ count: number }> {
    logger.info(`Deleting controls with ids ${ids}`)

    // Check if user is the author of the control
    const controls = await prisma.control.findMany({
        where: { id: { in: ids } },
        select: {
            id: true,
            authorId: true
        }
    })

    const controlIds = controls.map(control => control.id)

    if (controlIds.length !== ids.length) {
        throw new Error('Some controls do not exist')
    }

    if (controls.some(control => control.authorId !== userId)) {
        throw new Error('User is not the author of the control')
    }

    return await prisma.control.deleteMany({
        where: { id: { in: controlIds } }
    })
}

export async function fetchControlByPlannedEvaluationId(id: string) {
    const plannedEvaluation = await prisma.plannedEvaluation.findUnique({
        where: {
            id
        },
        include: {
            control: {
                include: {
                    exercises: {
                        include: {
                            questions: {
                                include: {
                                    medias: true
                                }
                            }
                        }
                    }
                }
            }
        }
    })
    return plannedEvaluation?.control
}

export async function fetchControlAndStudentSubmitionByControlIdAndStudentId(
    controlId: string,
    studentId: string
) {
    return await prisma.studentSubmission.findUnique({
        where: {
            studentId_controlId: {
                studentId: studentId,
                controlId: controlId
            }
        },
        include: {
            control: {
                include: {
                    exercises: {
                        include: {
                            questions: {
                                include: {
                                    medias: true
                                }
                            }
                        }
                    }
                }
            },
            answers: true
        }
    })
}

export async function fetchControlByClassId(classId: string) {
    return await prisma.control.findMany({
        where: { assignedClassId: classId }
    })
}
export async function fetchControlByClassIdNoAssigned(classId: string) {
    return await prisma.control.findMany({
        where: {
            assignedClassId: classId,
            OR: [{ status: 'DRAFT' }, { status: 'IN_PROGRESS' }]
        }
    })
}

export const fetchCommentProfEval = async ({
    studentId,
    controlId
}: {
    controlId: string
    studentId: string
}) => {
    return await prisma.studentSubmission.findUnique({
        where: {
            studentId_controlId: {
                studentId,
                controlId
            }
        },
        select: {
            globalFeedback: true,
            globalScore: true,
            plannedEvaluation: {
                select: {
                    availableDate: true,
                    dueDate: true
                }
            },
            control: {
                select: {
                    type: true,
                    name: true
                }
            },
            student: {
                select: {
                    firstName: true,
                    lastName: true
                }
            },
            answers: true
        }
    })
}

export async function getRandomTasksByChapterId(chapterId: string) {
    logger.info(`Getting random tasks for chapter with id ${chapterId}`)

    const tasks = await prisma.praxeoTask.findMany({
        where: {
            tmPart: {
                chapterId: chapterId
            }
        },
        include: {
            praxeoTaskSkills: {
                include: {
                    tmSkill: {
                        select: {
                            description: true
                        }
                    }
                }
            }
        }
    })

    if (tasks.length === 0) {
        logger.info(`No tasks found for chapter with id ${chapterId}`)
        return []
    }

    // Shuffle tasks and select a random number between 2 and 5
    const shuffledTasks = tasks.sort(() => 0.5 - Math.random())
    const numberOfTasksToSelect = Math.floor(Math.random() * 4) + 2 // Random number between 2 and 5
    const selectedTasks = shuffledTasks.slice(
        0,
        Math.min(numberOfTasksToSelect, shuffledTasks.length)
    )

    return selectedTasks.map(task => ({
        description: task.description,
        competencies: task.praxeoTaskSkills.map(ts => ts.tmSkill.description)
    }))
}

model ControlExerciseMedia {
  id       String            @id @default(dbgenerated("gen_random_uuid()"))
  fileName String?           @map("file_name")
  fileType String?           @map("file_type")
  data     Bytes?            @db.ByteA
  fileUrl  String?           @map("file_url")

  type                       ExerciseMediaType

  controlExerciseId String
  controlExercise   ControlExercise @relation(fields: [controlExerciseId], references: [id], onDelete: Cascade)

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  @@map("control_exercise_media")
}

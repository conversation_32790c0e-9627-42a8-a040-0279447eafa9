import { z } from "zod";

export const ClassSSEEvents = z.enum([
    "class:scheduled-evaluation",
    "class:updated-evaluation",
    "class:deleted-evaluation",
    "class:event-created",
    "class:event-updated",
    "class:event-deleted"
]);

export const ClassSSEPayload = z.object({
    classIds: z.array(z.string()),
    event: ClassSSEEvents,
    message: z.string().optional(),
    data: z.unknown().optional()
});

export type ClassSSEEvents = z.infer<typeof ClassSSEEvents>;
export type ClassSSEPayload<T = unknown> = z.infer<typeof ClassSSEPayload> & { data?: T };

import React, { ReactNode } from 'react'
import { cn } from '@/lib/utils/utils'

type IconeSwitchProps = {
    isActive: boolean
    iconEnable: ReactNode
    iconDisable: ReactNode
    className?: string
}

const IconeSwitch = ({
    isActive,
    iconEnable,
    iconDisable,
    className
}: IconeSwitchProps) => {
    return (
        <div className={cn('relative', className)}>
            <div
                className={cn(
                    'transition-all duration-200',
                    isActive ? 'scale-100 opacity-100' : 'scale-0 opacity-0'
                )}
            >
                {iconEnable}
            </div>
            <div
                className={cn(
                    'absolute inset-0 transition-all duration-200',
                    isActive ? 'scale-0 opacity-0' : 'scale-100 opacity-100'
                )}
            >
                {iconDisable}
            </div>
        </div>
    )
}

export default IconeSwitch

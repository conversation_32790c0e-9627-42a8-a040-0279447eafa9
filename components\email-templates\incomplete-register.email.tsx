import { UserTypeType } from '@/prisma/generated/zod/inputTypeSchemas/UserTypeSchema'
import {
    Body,
    But<PERSON>,
    Column,
    Container,
    Head,
    Heading,
    Html,
    Img,
    Link,
    Preview,
    Row,
    Section,
    Tailwind,
    Text
} from '@react-email/components'
import { getLocale, getTranslations } from 'next-intl/server'
import { getLangDir } from 'rtl-detect'

interface IncompleteRegisterEmailProps {
    name: string
    userType: UserTypeType
    emailSequence: 1 | 2 | 3 // J+1, J+3, J+7
}

// Définition des types pour le contenu des emails
interface BaseEmailContent {
    preview: string
    subject: string
    greeting: string
    intro: string
    buttonText: string
    mainTitle: string
    features: string[]
    bottomText: string
    signature: string
    team: string
    secondButton?: string
    ps?: string
    unsubscribe?: boolean
    extraButton?: string
}

export default async function IncompleteRegisterEmail({
    name,
    userType,
    emailSequence
}: IncompleteRegisterEmailProps) {
    const t = await getTranslations('email')
    const locale = await getLocale()
    const direction = getLangDir(locale)

    // URLs depuis les variables d'environnement
    const baseUrl = process.env.BASE_URL || 'http://localhost:3000'
    const legalNoticeUrl =
        process.env.EMAIL_LEGAL_NOTICE_URL ||
        'https://dinobot.fr/mentions-legales'
    const dataProtectionUrl =
        process.env.EMAIL_DATA_PROTECTION_URL ||
        'https://dinobot.fr/protection-donnees'
    const instagramUrl =
        process.env.EMAIL_INSTAGRAM_URL || 'https://instagram.com/dinobot'
    const linkedinUrl =
        process.env.EMAIL_LINKEDIN_URL || 'https://linkedin.com/company/dinobot'
    const facebookUrl =
        process.env.EMAIL_FACEBOOK_URL || 'https://facebook.com/dinobot'
    const youtubeUrl =
        process.env.EMAIL_YOUTUBE_URL || 'https://youtube.com/dinobot'

    // Configuration des contenus selon le type d'utilisateur et la séquence
    const emailContent: {
        student: Record<1 | 2 | 3, BaseEmailContent>
        teacher: Record<1 | 2 | 3, BaseEmailContent>
        establishment?: Record<1 | 2 | 3, BaseEmailContent>
    } = {
        student: {
            1: {
                preview: t('completeInscription.student.1.preview'),
                subject: t('completeInscription.student.1.subject'),
                greeting: t('completeInscription.student.1.greeting'),
                intro: t('completeInscription.student.1.intro'),
                buttonText: t('completeInscription.student.1.buttonText'),
                mainTitle: t('completeInscription.student.1.mainTitle'),
                features: t.raw('completeInscription.student.1.features'),
                bottomText: t('completeInscription.student.1.bottomText'),
                signature: t('completeInscription.student.1.signature'),
                team: t('completeInscription.student.1.team')
            },
            2: {
                preview: t('completeInscription.student.2.preview'),
                subject: t('completeInscription.student.2.subject'),
                greeting: t('completeInscription.student.2.greeting'),
                intro: t('completeInscription.student.2.intro'),
                buttonText: t('completeInscription.student.2.buttonText'),
                mainTitle: t('completeInscription.student.2.mainTitle'),
                features: t.raw('completeInscription.student.2.features'),
                bottomText: t('completeInscription.student.2.bottomText'),
                signature: t('completeInscription.student.2.signature'),
                team: t('completeInscription.student.2.team'),
                ps: t('completeInscription.student.2.ps')
            },
            3: {
                preview: t('completeInscription.student.3.preview'),
                subject: t('completeInscription.student.3.subject'),
                greeting: t('completeInscription.student.3.greeting'),
                intro: t('completeInscription.student.3.intro'),
                buttonText: t('completeInscription.student.3.buttonText'),
                mainTitle: t('completeInscription.student.3.mainTitle'),
                features: t.raw('completeInscription.student.3.features'),
                bottomText: t('completeInscription.student.3.bottomText'),
                signature: t('completeInscription.student.3.signature'),
                team: t('completeInscription.student.3.team'),
                unsubscribe: true,
                extraButton: t('completeInscription.student.3.extraButton')
            }
        },
        teacher: {
            1: {
                preview: t('completeInscription.teacher.1.preview'),
                subject: t('completeInscription.teacher.1.subject'),
                greeting: t('completeInscription.teacher.1.greeting', { name }),
                intro: t('completeInscription.teacher.1.intro'),
                buttonText: t('completeInscription.teacher.1.buttonText'),
                mainTitle: t('completeInscription.teacher.1.mainTitle'),
                features: t.raw('completeInscription.teacher.1.features'),
                bottomText: t('completeInscription.teacher.1.bottomText'),
                signature: t('completeInscription.teacher.1.signature'),
                team: t('completeInscription.teacher.1.team'),
                secondButton: t('completeInscription.teacher.1.secondButton')
            },
            2: {
                preview: t('completeInscription.teacher.2.preview'),
                subject: t('completeInscription.teacher.2.subject'),
                greeting: t('completeInscription.teacher.2.greeting', { name }),
                intro: t('completeInscription.teacher.2.intro'),
                buttonText: t('completeInscription.teacher.2.buttonText'),
                mainTitle: t('completeInscription.teacher.2.mainTitle'),
                features: t.raw('completeInscription.teacher.2.features'),
                bottomText: t('completeInscription.teacher.2.bottomText'),
                signature: t('completeInscription.teacher.2.signature'),
                team: t('completeInscription.teacher.2.team'),
                secondButton: t('completeInscription.teacher.2.secondButton')
            },
            3: {
                preview: t('completeInscription.teacher.3.preview'),
                subject: t('completeInscription.teacher.3.subject'),
                greeting: t('completeInscription.teacher.3.greeting', { name }),
                intro: t('completeInscription.teacher.3.intro'),
                buttonText: t('completeInscription.teacher.3.buttonText'),
                mainTitle: t('completeInscription.teacher.3.mainTitle'),
                features: t.raw('completeInscription.teacher.3.features'),
                bottomText: t('completeInscription.teacher.3.bottomText'),
                signature: t('completeInscription.teacher.3.signature'),
                team: t('completeInscription.teacher.3.team'),
                secondButton: t('completeInscription.teacher.3.secondButton'),
                unsubscribe: true
            }
        }
    }

    const content = emailContent[userType]![emailSequence]

    return (
        <Html dir={direction}>
            <Head />
            <Preview>{content.preview}</Preview>
            <Tailwind>
                <Body className="bg-white font-sans">
                    <Container className="mx-auto p-0 max-w-[600px]">
                        {/* Blue header with greeting */}
                        <Section className="bg-blue-600 p-5 text-center text-white">
                            <Heading className="text-3xl font-bold my-2.5 text-white">
                                {content.greeting}
                            </Heading>
                        </Section>

                        {/* Main content */}
                        <Section className="p-5">
                            <Text className="text-base leading-6 text-gray-800 my-5 whitespace-pre-line break-words hyphens-auto w-full max-w-full block">
                                {content.intro}
                            </Text>

                            {/* CTA Button */}
                            <Section className="text-center my-7">
                                <Button
                                    className="bg-yellow-400 rounded-full text-black text-base font-bold no-underline inline-block px-5 py-3"
                                    href={`${baseUrl}/`}
                                >
                                    {content.buttonText}
                                </Button>
                            </Section>

                            <Text className="text-base leading-6 text-gray-800 my-5">
                                {content.mainTitle}
                            </Text>

                            {/* Benefits section */}
                            <Section className="my-5">
                                {content.features.map(
                                    (feature: string, index: number) => (
                                        <Row
                                            key={index}
                                            className={index > 0 ? 'mt-5' : ''}
                                        >
                                            <Column className="align-baseline w-[24px]">
                                                <Text className="m-0 text-[16px] text-blue-600">
                                                    •
                                                </Text>
                                            </Column>
                                            <Column className="align-middle pl-5">
                                                <Text className="text-base font-bold m-0 text-gray-800">
                                                    {feature}
                                                </Text>
                                            </Column>
                                        </Row>
                                    )
                                )}
                            </Section>

                            {/* Second Button for teacher */}
                            {content.secondButton && (
                                <Section className="text-center my-7">
                                    <Button
                                        className="bg-blue-600 rounded-full text-white text-base font-bold no-underline inline-block px-5 py-3"
                                        href={`${baseUrl}/my-classes`}
                                    >
                                        {content.secondButton}
                                    </Button>
                                </Section>
                            )}

                            {content.bottomText && (
                                <Text className="text-base leading-6 text-gray-800 my-5 whitespace-pre-line">
                                    {content.bottomText}
                                </Text>
                            )}

                            {/* Special button for student sequence 3 */}
                            {content.extraButton &&
                                emailSequence === 3 &&
                                userType === 'student' && (
                                    <Section className="text-center my-7">
                                        <Button
                                            className="bg-yellow-400 rounded-full text-black text-base font-bold no-underline inline-block px-5 py-3"
                                            href={`${baseUrl}/login`}
                                        >
                                            {content.extraButton}
                                        </Button>
                                    </Section>
                                )}

                            <Text className="text-base leading-6 text-gray-800 my-5">
                                {content.signature}
                            </Text>

                            <Text className="text-base italic my-5 text-gray-800">
                                -{content.team}
                            </Text>

                            {/* PS for student sequence 2 */}
                            {content.ps && (
                                <Text className="text-sm italic my-5 text-gray-600">
                                    {content.ps}
                                </Text>
                            )}

                            {/* Logo */}
                            <Section className="text-center my-7 w-full flex item-center justify-center">
                                <Img
                                    src={`${baseUrl}/dinobot-logo-small.svg`}
                                    width="60"
                                    height="60"
                                    alt="Logo Dinobot"
                                />
                            </Section>
                        </Section>

                        {/* Social media footer */}
                        <Section className="bg-blue-600 p-5 text-center text-white">
                            <Text className="text-base font-bold mb-4 text-white">
                                {t('common.followUs')}
                            </Text>
                            <Row className="w-60 mx-auto">
                                <Column className="w-15 text-center">
                                    <Link href={instagramUrl}>
                                        <Img
                                            src={`${baseUrl}/instagram.png`}
                                            width="32"
                                            height="32"
                                            alt="Instagram"
                                        />
                                    </Link>
                                </Column>
                                <Column className="w-15 text-center">
                                    <Link href={linkedinUrl}>
                                        <Img
                                            src={`${baseUrl}/linkedin.png`}
                                            width="32"
                                            height="32"
                                            alt="LinkedIn"
                                        />
                                    </Link>
                                </Column>
                                <Column className="w-15 text-center">
                                    <Link href={facebookUrl}>
                                        <Img
                                            src={`${baseUrl}/facebook.png`}
                                            width="32"
                                            height="32"
                                            alt="Facebook"
                                        />
                                    </Link>
                                </Column>
                                <Column className="w-15 text-center">
                                    <Link href={youtubeUrl}>
                                        <Img
                                            src={`${baseUrl}/youtube.png`}
                                            width="32"
                                            height="32"
                                            alt="YouTube"
                                        />
                                    </Link>
                                </Column>
                            </Row>
                        </Section>

                        {/* Legal footer */}
                        <Section className="p-5 text-center bg-gray-100">
                            <Text className="text-base font-bold m-0 text-gray-800">
                                {t('common.company')}
                            </Text>
                            <Text className="text-sm my-1 mb-5 text-gray-600">
                                {t('common.address')}
                            </Text>

                            <Section className="my-2.5">
                                <Link
                                    href={legalNoticeUrl}
                                    className="text-blue-600 no-underline"
                                >
                                    {t('common.legalNotice')}
                                </Link>{' '}
                                |{' '}
                                <Link
                                    href={dataProtectionUrl}
                                    className="text-blue-600 no-underline"
                                >
                                    {t('common.dataProtection')}
                                </Link>
                            </Section>

                            <Text className="text-xs text-gray-500 my-5 mt-1">
                                {t('common.automaticEmail')}
                            </Text>

                            {content.unsubscribe && (
                                <Text className="text-xs text-gray-500 my-1">
                                    {t('common.unsubscribeText')}{' '}
                                    <Link
                                        href={`${baseUrl}/`}
                                        className="text-gray-600 underline"
                                    >
                                        {t('common.unsubscribeLink')}
                                    </Link>
                                </Text>
                            )}
                        </Section>
                    </Container>
                </Body>
            </Tailwind>
        </Html>
    )
}

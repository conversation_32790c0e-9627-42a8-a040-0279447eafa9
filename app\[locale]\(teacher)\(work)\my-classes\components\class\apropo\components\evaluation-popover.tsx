import { Button } from '@/components/ui/button'
import { ControlPartial } from '@/prisma/generated/zod/modelSchema/ControlSchema'
import { CalendarPlus2, SquarePen, Trash2 } from 'lucide-react'
import React, { useReducer } from 'react'
import { getControlById } from '@/lib/control-mode/actions'
import { usePathname, useRouter } from '@/i18n/routing'
import { selectUseScheduleAssessmentStore } from '../../../../[classId]/(evaluation)/store/use-schedule-assessment-store'
import { useTranslations } from 'next-intl'
import { useQuery } from '@tanstack/react-query'
import { toast } from 'sonner'
import { useApropoStore } from '../../../../store/apropos.store'
import { deleteControl } from '@/lib/control-mode/actions'
import { EvaluationAlert } from './evaluation-alert'
import ConfirmDialog from './confirm-dialog'
import { useCookies } from 'next-client-cookies'
import { Skeleton } from '@/components/ui/skeleton'
import EvaluationElementPreview from './evaluation-elemnt-preview'
import { classEmit } from '@/lib/sse/class/class-events'

type evaluationPopoverProps = {
    evaluation: ControlPartial
}
function isAnyEvaluationAssigned(
    checkedEvaluations: string | undefined,
    controls: ControlPartial[] = []
): boolean {
    return controls.some(
        control =>
            checkedEvaluations?.includes(control.id!) &&
            control.status === 'ASSIGNED'
    )
}

export type DialogState = {
    deleteDialogOpen: boolean
    evaluationAlertOpen: boolean
}

const initialState: DialogState = {
    deleteDialogOpen: false,
    evaluationAlertOpen: false
}

const EvaluationPopover = ({ evaluation }: evaluationPopoverProps) => {
    const te = useTranslations('teacher.myClass.apropo.eval_card')
    const t = useTranslations('teacher.myClass.apropo')

    const route = useRouter()
    const path = usePathname()
    const cookies = useCookies()
    const neverAskDeleteDialog = cookies.get('never-ask-delete-dialog')
    const { setControls, controls } = useApropoStore()
    const [dialogState, dispatch] = useReducer(
        (state: DialogState, action: Partial<DialogState>) => ({
            ...state,
            ...action
        }),
        initialState
    )

    const setSelectedControl =
        selectUseScheduleAssessmentStore.use.setSelectedControl()
    const { data: previewEval, isLoading } = useQuery({
        queryKey: ['getControlById', evaluation?.id],
        queryFn: () => getControlById(evaluation?.id)
    })
    const updateEvaluation = () => {
        route.push(`${path}/create-evaluation-next?evalId=${evaluation?.id}`)
    }
    const plananedEvaluation = () => {
        setSelectedControl(evaluation)
        route.push(`${path}/schedule-assessment`)
    }
    const onDelete = async () => {
        try {
            if (
                neverAskDeleteDialog === 'true' &&
                !isAnyEvaluationAssigned(evaluation?.id, controls)
            ) {
                const res = await deleteControl(evaluation.id!)

                if (res && 'error' in res) {
                    throw new Error(res.error)
                }
                await classEmit({
                    classIds: [evaluation.assignedClassId!],
                    event: 'class:deleted-evaluation',
                    data: evaluation.themeId
                })

                if (dispatch) dispatch({ deleteDialogOpen: false })
                if (setControls)
                    setControls(
                        controls.filter(
                            control => !evaluation?.id!.includes(control.id!)
                        )
                    )
                toast('Suppression effectuée')
                return
            }

            if (
                (!neverAskDeleteDialog || neverAskDeleteDialog === 'false') &&
                !isAnyEvaluationAssigned(evaluation?.id, controls)
            ) {
                dispatch({ deleteDialogOpen: true })
            } else if (isAnyEvaluationAssigned(evaluation?.id, controls)) {
                dispatch({ evaluationAlertOpen: true })
            }
        } catch (error) {
            toast('unexpected error', { description: (error as Error).message })
        }
    }
    if (isLoading)
        return (
            <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 rounded-md" /> {/* Icône */}
                <Skeleton className="h-5 w-24 rounded-md" /> {/* Texte */}
            </div>
        )
    return (
        <>
            <EvaluationAlert
                title={t('alert.title')}
                description={t('alert.description')}
                cancelLabel={t('alert.ok')}
                evaluationAlertOpen={dialogState.evaluationAlertOpen}
                dispatch={dispatch}
            />
            <ConfirmDialog
                deleteDialogOpen={dialogState.deleteDialogOpen}
                dispatch={dispatch}
            />
            {previewEval?.submissions?.length === 0 && (
                <Button
                    variant="ghost"
                    className="p-0 flex gap-2 justify-start"
                    onClick={updateEvaluation}
                >
                    <SquarePen />
                    {te('update')}
                </Button>
            )}
            <EvaluationElementPreview id={evaluation?.id} loading={isLoading} />
            {evaluation?.status !== 'ASSIGNED' && (
                <Button
                    variant="ghost"
                    className="p-0 flex gap-2 justify-start"
                    onClick={plananedEvaluation}
                >
                    <CalendarPlus2 />
                    {te('program')}
                </Button>
            )}
            <Button
                variant="ghost"
                className="p-0 flex gap-2 justify-start text-dinoBotRed hover:text-dinoBotRed/80"
                onClick={onDelete}
            >
                <Trash2 />
                {te('delete')}
            </Button>
        </>
    )
}

export default EvaluationPopover

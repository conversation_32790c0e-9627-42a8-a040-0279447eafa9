import DataTable from '@/components/shared/ui/data-table'
import { Button } from '@/components/ui/button'
import {
    <PERSON>alog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger
} from '@/components/ui/dialog'
import { getRequestsLevelDomain } from '@/lib/requests-level-domain/action'
import { getLangProps } from '@/lib/utils/string.utils'
import { RequestStatusType } from '@/prisma/generated/zod/inputTypeSchemas/RequestStatusSchema'
import { RequestLevelDomainItemPartialWithRelations } from '@/prisma/generated/zod/modelSchema/RequestLevelDomainItemSchema'
import { RequestsLevelDomainWithPartialRelations } from '@/prisma/generated/zod/modelSchema/RequestsLevelDomainSchema'
import { useQuery } from '@tanstack/react-query'
import { ColumnDef } from '@tanstack/react-table'
import { useLocale, useTranslations } from 'next-intl'
import React from 'react'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger
} from '@/components/ui/tooltip'
import { IconInfo } from '@/components/ui/icons'

const HistoryRequest = () => {
    const t = useTranslations(`teacher`)
    const lang = useLocale()

    const { data, refetch } = useQuery({
        queryKey: ['historyRequests'],
        queryFn: () => getRequestsLevelDomain()
    })
    const columns: ColumnDef<RequestsLevelDomainWithPartialRelations>[] = [
        {
            accessorKey: 'id'
        },
        {
            accessorKey: 'date',
            accessorFn: row => row.createdAt,
            id: 'date',
            header: t('register.history.date'),
            size: 50,
            minSize: 10,
            maxSize: 80,
            cell: ({ row }) => {
                const date: Date = row.getValue('date')
                return date.toLocaleDateString('fr-FR', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                })
            }
        },
        {
            accessorKey: 'requestLevelDomains',
            accessorFn: row => row.requestLevelDomains,
            header: t('register.history.domain_level'),
            id: 'domain_level',
            cell: ({ row }) => {
                const requestLevelDomains:
                    | RequestLevelDomainItemPartialWithRelations[]
                    | undefined = row.getValue('domain_level')
                if (!requestLevelDomains || requestLevelDomains.length === 0) {
                    return t('register.history.no_domain_level')
                }
                // Group by domain
                const domainGroups = requestLevelDomains.reduce(
                    (acc, requestLevelDomain) => {
                        const domainName = getLangProps({
                            obj: requestLevelDomain.levelDomain?.domain!,
                            base: 'name',
                            lang
                        })
                        const levelName = getLangProps({
                            obj: requestLevelDomain.levelDomain?.level!,
                            base: 'name',
                            lang
                        })

                        if (domainName && levelName) {
                            if (!acc[domainName]) {
                                acc[domainName] = []
                            }
                            acc[domainName].push(levelName)
                        }
                        return acc
                    },
                    {} as Record<string, string[]>
                )

                return Object.entries(domainGroups).map(([domain, levels]) => (
                    <span key={domain} className="text-sm block">
                        - {domain} ({levels.join(', ')})
                    </span>
                ))
            }
        },
        {
            accessorKey: 'status',
            accessorFn: row => ({ status: row.status, comment: row.comment }),
            header: t('register.history.status.title'),
            size: 50,
            minSize: 20,
            maxSize: 80,
            cell: ({ row }) => {
                const {
                    status,
                    comment
                }: { status: RequestStatusType; comment: string } =
                    row.getValue('status')
                return (
                    <div className="flex items-center gap-2">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Badge
                                        className={`${getStatusColor(status)} font-medium`}
                                    >
                                        {t(`register.history.status.${status}`)}
                                    </Badge>
                                </TooltipTrigger>
                                {comment && (
                                    <TooltipContent className="bg-dinoBotBlue text-white">
                                        <p>{comment}</p>
                                    </TooltipContent>
                                )}
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                )
            }
        }
    ]
    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button
                    size="sm"
                    className="bg-dinoBotBlue hover:bg-dinoBotVibrantBlue "
                    onClick={() => refetch()}
                >
                    {t('register.history.trigger')}
                </Button>
            </DialogTrigger>
            <DialogContent className="min-w-[900px]">
                <DialogHeader>
                    <DialogTitle className="flex text-lg justify-center">
                        {t('register.history.title')}
                    </DialogTitle>
                    <DialogDescription></DialogDescription>
                </DialogHeader>
                <ScrollArea className="h-[500px] 2xl:h-[700px] w-full">
                    <DataTable
                        columns={columns}
                        data={data as RequestsLevelDomainWithPartialRelations[]}
                        emptyMessage={t('register.history.empty')}
                    />
                </ScrollArea>
                <DialogFooter></DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

const getStatusColor = (status: RequestStatusType) => {
    switch (status) {
        case 'PENDING':
            return 'bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-100 focus:bg-yellow-100'
        case 'ACCEPTED':
            return 'bg-green-100 text-green-800 border-green-200 hover:bg-green-100 focus:bg-green-100'
        case 'REJECTED':
            return 'bg-red-100 text-red-800 border-red-200 hover:bg-red-100 focus:bg-red-100'
        case 'CANCELED':
            return 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-100 focus:bg-gray-100'
        default:
            return 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-100 focus:bg-gray-100'
    }
}

export default HistoryRequest

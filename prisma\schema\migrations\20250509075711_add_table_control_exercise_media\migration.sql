-- AlterTable
ALTER TABLE "control_question_media" ADD COLUMN     "file_url" TEXT;

-- CreateTable
CREATE TABLE "control_exercise_media" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "file_name" TEXT,
    "file_type" TEXT,
    "data" BYTEA,
    "file_url" TEXT,
    "type" "ExerciseMediaType" NOT NULL,
    "controlExerciseId" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3),

    CONSTRAINT "control_exercise_media_pkey" PRIMARY KEY ("id")
);

-- AddForeign<PERSON>ey
ALTER TABLE "control_exercise_media" ADD CONSTRAINT "control_exercise_media_controlExerciseId_fkey" FOREIGN KEY ("controlExerciseId") REFERENCES "control_exercises"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

import { z } from 'zod';
import { RequestsLevelDomainWithRelationsSchema, RequestsLevelDomainPartialWithRelationsSchema, RequestsLevelDomainOptionalDefaultsWithRelationsSchema } from './RequestsLevelDomainSchema'
import type { RequestsLevelDomainWithRelations, RequestsLevelDomainPartialWithRelations, RequestsLevelDomainOptionalDefaultsWithRelations } from './RequestsLevelDomainSchema'
import { LevelDomainWithRelationsSchema, LevelDomainPartialWithRelationsSchema, LevelDomainOptionalDefaultsWithRelationsSchema } from './LevelDomainSchema'
import type { LevelDomainWithRelations, LevelDomainPartialWithRelations, LevelDomainOptionalDefaultsWithRelations } from './LevelDomainSchema'

/////////////////////////////////////////
// REQUEST LEVEL DOMAIN ITEM SCHEMA
/////////////////////////////////////////

export const RequestLevelDomainItemSchema = z.object({
  id: z.string(),
  requestId: z.string(),
  levelDomainId: z.string(),
  createdAt: z.coerce.date(),
})

export type RequestLevelDomainItem = z.infer<typeof RequestLevelDomainItemSchema>

/////////////////////////////////////////
// REQUEST LEVEL DOMAIN ITEM PARTIAL SCHEMA
/////////////////////////////////////////

export const RequestLevelDomainItemPartialSchema = RequestLevelDomainItemSchema.partial()

export type RequestLevelDomainItemPartial = z.infer<typeof RequestLevelDomainItemPartialSchema>

/////////////////////////////////////////
// REQUEST LEVEL DOMAIN ITEM OPTIONAL DEFAULTS SCHEMA
/////////////////////////////////////////

export const RequestLevelDomainItemOptionalDefaultsSchema = RequestLevelDomainItemSchema.merge(z.object({
  id: z.string().optional(),
  createdAt: z.coerce.date().optional(),
}))

export type RequestLevelDomainItemOptionalDefaults = z.infer<typeof RequestLevelDomainItemOptionalDefaultsSchema>

/////////////////////////////////////////
// REQUEST LEVEL DOMAIN ITEM RELATION SCHEMA
/////////////////////////////////////////

export type RequestLevelDomainItemRelations = {
  request: RequestsLevelDomainWithRelations;
  levelDomain: LevelDomainWithRelations;
};

export type RequestLevelDomainItemWithRelations = z.infer<typeof RequestLevelDomainItemSchema> & RequestLevelDomainItemRelations

export const RequestLevelDomainItemWithRelationsSchema: z.ZodType<RequestLevelDomainItemWithRelations> = RequestLevelDomainItemSchema.merge(z.object({
  request: z.lazy(() => RequestsLevelDomainWithRelationsSchema),
  levelDomain: z.lazy(() => LevelDomainWithRelationsSchema),
}))

/////////////////////////////////////////
// REQUEST LEVEL DOMAIN ITEM OPTIONAL DEFAULTS RELATION SCHEMA
/////////////////////////////////////////

export type RequestLevelDomainItemOptionalDefaultsRelations = {
  request: RequestsLevelDomainOptionalDefaultsWithRelations;
  levelDomain: LevelDomainOptionalDefaultsWithRelations;
};

export type RequestLevelDomainItemOptionalDefaultsWithRelations = z.infer<typeof RequestLevelDomainItemOptionalDefaultsSchema> & RequestLevelDomainItemOptionalDefaultsRelations

export const RequestLevelDomainItemOptionalDefaultsWithRelationsSchema: z.ZodType<RequestLevelDomainItemOptionalDefaultsWithRelations> = RequestLevelDomainItemOptionalDefaultsSchema.merge(z.object({
  request: z.lazy(() => RequestsLevelDomainOptionalDefaultsWithRelationsSchema),
  levelDomain: z.lazy(() => LevelDomainOptionalDefaultsWithRelationsSchema),
}))

/////////////////////////////////////////
// REQUEST LEVEL DOMAIN ITEM PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type RequestLevelDomainItemPartialRelations = {
  request?: RequestsLevelDomainPartialWithRelations;
  levelDomain?: LevelDomainPartialWithRelations;
};

export type RequestLevelDomainItemPartialWithRelations = z.infer<typeof RequestLevelDomainItemPartialSchema> & RequestLevelDomainItemPartialRelations

export const RequestLevelDomainItemPartialWithRelationsSchema: z.ZodType<RequestLevelDomainItemPartialWithRelations> = RequestLevelDomainItemPartialSchema.merge(z.object({
  request: z.lazy(() => RequestsLevelDomainPartialWithRelationsSchema),
  levelDomain: z.lazy(() => LevelDomainPartialWithRelationsSchema),
})).partial()

export type RequestLevelDomainItemOptionalDefaultsWithPartialRelations = z.infer<typeof RequestLevelDomainItemOptionalDefaultsSchema> & RequestLevelDomainItemPartialRelations

export const RequestLevelDomainItemOptionalDefaultsWithPartialRelationsSchema: z.ZodType<RequestLevelDomainItemOptionalDefaultsWithPartialRelations> = RequestLevelDomainItemOptionalDefaultsSchema.merge(z.object({
  request: z.lazy(() => RequestsLevelDomainPartialWithRelationsSchema),
  levelDomain: z.lazy(() => LevelDomainPartialWithRelationsSchema),
}).partial())

export type RequestLevelDomainItemWithPartialRelations = z.infer<typeof RequestLevelDomainItemSchema> & RequestLevelDomainItemPartialRelations

export const RequestLevelDomainItemWithPartialRelationsSchema: z.ZodType<RequestLevelDomainItemWithPartialRelations> = RequestLevelDomainItemSchema.merge(z.object({
  request: z.lazy(() => RequestsLevelDomainPartialWithRelationsSchema),
  levelDomain: z.lazy(() => LevelDomainPartialWithRelationsSchema),
}).partial())

export default RequestLevelDomainItemSchema;

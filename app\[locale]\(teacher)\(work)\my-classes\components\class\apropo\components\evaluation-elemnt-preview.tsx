import { getControlByIdWithMedia } from '@/lib/control-mode/actions'
import { useQuery } from '@tanstack/react-query'
import React from 'react'
import DialogPreviewEval from '../../../../[classId]/(evaluation)/components/dialogs/dialog-preview-eval'
import { Domain } from '@/prisma/generated/zod/modelSchema/DomainSchema'
import { Level } from '@/prisma/generated/zod/modelSchema/LevelSchema'
import { Eye } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Skeleton } from '@/components/ui/skeleton'

type EvaluationElementPreviewProps = {
    id: string | undefined
    loading: boolean
}

const EvaluationElementPreview = ({
    id,
    loading
}: EvaluationElementPreviewProps) => {
    const te = useTranslations('teacher.myClass.apropo.eval_card')
    const { data: previewEval, isLoading } = useQuery({
        queryKey: ['getControlByIdWithMedia', id],
        enabled: !loading,
        refetchOnWindowFocus: true,
        queryFn: () => getControlByIdWithMedia(id)
    })
    if (isLoading)
        return (
            <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4 rounded-md" /> {/* Icône */}
                <Skeleton className="h-5 w-24 rounded-md" /> {/* Texte */}
            </div>
        )
    return (
        <DialogPreviewEval
            exos={previewEval?.exercises ?? []}
            domain={previewEval?.assignedClass?.domain as Domain}
            level={previewEval?.assignedClass?.level as Level}
            name={previewEval?.name}
            font={previewEval?.fontName ?? 'Roboto'}
            fontSize={previewEval?.fontSize ?? 12}
        >
            <div className="p-0 flex gap-2 justify-start hover:bg-accent hover:text-accent-foreground h-9  items-center whitespace-nowrap rounded-md text-sm font-medium">
                <Eye />
                {te('preview')}
            </div>
        </DialogPreviewEval>
    )
}

export default EvaluationElementPreview

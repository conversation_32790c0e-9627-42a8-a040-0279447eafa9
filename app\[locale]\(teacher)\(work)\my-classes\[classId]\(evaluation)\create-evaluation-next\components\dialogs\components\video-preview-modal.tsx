'use client'

import React from 'react'
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogClose
} from '@/components/ui/dialog'
import { X } from 'lucide-react'

type VideoPreviewModalProps = {
    isOpen: boolean
    onClose: () => void
    videoUrl?: string
    videoName?: string
}

export function VideoPreviewModal({
    isOpen,
    onClose,
    videoUrl,
    videoName = 'Video Preview'
}: VideoPreviewModalProps) {
    if (!isOpen || !videoUrl) {
        return null
    }

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[80vw] md:max-w-[70vw] lg:max-w-[60vw] xl:max-w-[50vw] p-0">
                <DialogHeader className="p-4 border-b flex flex-row justify-between items-center">
                    <DialogTitle className="truncate pr-8">
                        {videoName}
                    </DialogTitle>
                </DialogHeader>
                <div className="p-4 bg-black flex justify-center items-center">
                    <video
                        src={videoUrl}
                        controls
                        autoPlay
                        muted={false}
                        className="max-w-full max-h-[80vh]"
                    />
                </div>
            </DialogContent>
        </Dialog>
    )
}

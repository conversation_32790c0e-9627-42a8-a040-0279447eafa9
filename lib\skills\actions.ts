'use server'
import { SkillWithRelationsSchema } from '@/prisma/generated/zod/modelSchema/SkillSchema'
import { z } from 'zod'
import { fetchSkill, fetchSkills } from './server'
import { auth } from '@/auth'
import { logger } from '@/logger/logger'
import { UnauthorizedError } from '../utils/errors'

async function checkAuth() {
    const session = await auth()
    if (!session?.user) {
        logger.error('Unauthorized access attempt')
        throw new UnauthorizedError('Unauthorized')
    }
    return session.user
}

/**
 * @returns all skills from database
 * @throws UnauthorizedError if user is not authenticated
 */
export async function getSkills(): Promise<
    | z.infer<typeof SkillWithRelationsSchema>[]
    | { error: string; status: number }
> {
    try {
        const user = await checkAuth()
        logger.debug(
            `Request to get all school cycles from user: ${user.email}`
        )

        return await fetchSkills()
    } catch (error) {
        logger.error(`Error fetching skills: ${error}`)
        if (error instanceof UnauthorizedError) {
            return { error: error.message, status: error.statusCode }
        }
        return {
            error: 'Internal Server Error',
            status: 500
        }
    }
}

/**
 * @param id - id of the skill
 * @returns the skill with the given id
 * @throws UnauthorizedError if user is not authenticated
 */
export async function getSkill(id: string) {
    try {
        const user = await checkAuth()
        logger.debug(
            `Request to get all school cycles from user: ${user.email}`
        )

        return await fetchSkill(id)
    } catch (error) {
        logger.error(`Error fetching skills: ${error}`)
        if (error instanceof UnauthorizedError) {
            return { error: error.message, status: error.statusCode }
        }
        return {
            error: 'Internal Server Error',
            status: 500
        }
    }
}

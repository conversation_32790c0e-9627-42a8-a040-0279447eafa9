import prisma from '../prisma-client'
import { logger } from '../../logger/logger'

export async function fetchPraxeosByPartId(partId: string) {
    return await prisma.praxeoStructs.findMany({
        where: { partId },
        select: {
            id: true,
            contents: true,
            part: {
                select: {
                    name: true
                }
            }
        }
    })
}

export async function fetchPraxeosByChapterId(chapterId: string) {
    const count = await prisma.praxeoStructs.count({
        where: {
            part: {
                chapterId
            }
        }
    })
    const skip = Math.floor(Math.random() * count)
    return await prisma.praxeoStructs.findMany({
        where: {
            part: {
                chapterId
            }
        },
        skip: skip,
        take: 4,
        select: {
            contents: true,
            part: {
                select: {
                    name: true
                }
            }
        }
    })
}

export async function fetchTasksWithCompetenciesAndExamples(partId: string) {
    logger.debug('Starting task fetch for partId:', partId)
    try {
        logger.debug('Fetching tasks from praxeo_tasks table')
        const tasks = await prisma.praxeoTask.findMany({
            where: {
                tmPartId: partId,
                isDisabled: false
            },
            include: {
                praxeoTaskSkills: {
                    include: {
                        tmSkill: {
                            select: {
                                description: true
                            }
                        }
                    }
                }
            }
        })

        logger.debug(
            'Fetching competencies from praxeo_task_skills and tm_skills'
        )
        const formattedTasks = tasks.map((task: any) => {
            const competencies = task.praxeoTaskSkills.map((ts: any) => ({
                description: ts.tmSkill.description
            }))

            logger.debug('Filtering examples to normal difficulty')
            const examples = ((task.examples as any[]) || []).filter(
                example => example.niveau_difficulte === 'normal'
            )

            const {
                praxeoTaskSkills,
                examples: originalExamples,
                ...rest
            } = task
            return {
                ...rest,
                competencies,
                examples
            }
        })

        logger.debug('Tasks fetched successfully:', formattedTasks.length)
        return formattedTasks
    } catch (error) {
        logger.error('Error fetching tasks:', error)
        throw error
    }
}

export async function fetchTasksWithCompetenciesAndExamplesByDifficulty(
    partId: string,
    difficulty: 0 | 1 | 2 | 3
) {
    logger.debug(
        `Starting task fetch for partId: ${partId} with difficulty: ${difficulty}`
    )
    const takeCount = process.env.CONTROL_MODE_EXERCISE_NUMBER
        ? parseInt(process.env.CONTROL_MODE_EXERCISE_NUMBER)
        : undefined
    try {
        logger.debug('Fetching tasks from praxeo_tasks table')
        const tasks = await prisma.praxeoTask.findMany({
            where: {
                tmPartId: partId,
                isDisabled: false
            },
            include: {
                praxeoTaskSkills: {
                    include: {
                        tmSkill: {
                            select: {
                                description: true
                            }
                        }
                    }
                }
            },
            take: takeCount
        })

        logger.debug(
            'Fetching competencies from praxeo_task_skills and tm_skills'
        )
        const formattedTasks = tasks.map((task: any) => {
            const competencies = task.praxeoTaskSkills.map((ts: any) => ({
                description: ts.tmSkill.description
            }))

            let difficultyLevel: string
            if (difficulty === 0) {
                difficultyLevel = 'easy'
            } else if (difficulty === 1 || difficulty === 2) {
                difficultyLevel = 'normal'
            } else {
                difficultyLevel = 'hard'
            }

            logger.debug(`Filtering examples to difficulty: ${difficultyLevel}`)
            const examples = ((task.examples as any[]) || []).filter(
                example => example.niveau_difficulte === difficultyLevel
            )

            const {
                praxeoTaskSkills,
                examples: originalExamples,
                ...rest
            } = task
            return {
                ...rest,
                competencies,
                examples
            }
        })

        logger.debug('Tasks fetched successfully:', formattedTasks.length)
        return formattedTasks
    } catch (error) {
        logger.error('Error fetching tasks:', error)
        throw error
    }
}

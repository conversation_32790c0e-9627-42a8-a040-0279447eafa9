'server-only'

import {
    PutO<PERSON><PERSON>ommand,
    GetO<PERSON><PERSON>ommand,
    DeleteO<PERSON>Command,
    ListObjectsV2Command,
    ListObjectsV2CommandInput,
    PutObjectCommandInput,
    DeleteObjectCommandInput,
    GetObjectCommandInput
} from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { s3Client } from './client'

const BUCKET = process.env.AWS_S3_BUCKET_NAME!

export async function uploadFile(file: File, path?: string) {
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    const params: PutObjectCommandInput = {
        Bucket: BUCKET,
        Key: path || file.name,
        Body: buffer,
        ContentType: file.type
    }

    try {
        await s3Client.send(new PutObjectCommand(params))
        return { success: true, path: params.Key }
    } catch (error) {
        console.error('S3 Upload Error:', error)
        return { success: false, error }
    }
}

export async function downloadFile(key: string) {
    const params: GetObjectCommandInput = {
        Bucket: BUCKET,
        Key: key
    }
    try {
        const response = await s3Client.send(new GetObjectCommand(params))

        if (!response.Body) {
            return {
                success: false,
                error: new Error('No file content found')
            }
        }

        const arrayBuffer = await response.Body.transformToByteArray()
        const file = new File([arrayBuffer], key, {
            type: response.ContentType || 'application/octet-stream'
        })

        return {
            success: true,
            file,
            contentType: response.ContentType,
            lastModified: response.LastModified
        }
    } catch (error) {
        console.error('S3 Download Error:', error)
        return { success: false, error }
    }
}

export async function getSignedUrlForDownload(
    key: string,
    expiresIn: number = 3600
) {
    const params: GetObjectCommandInput = {
        Bucket: BUCKET,
        Key: key
    }

    try {
        const url = await getSignedUrl(
            s3Client as any,
            new GetObjectCommand(params),
            {
                expiresIn
            }
        )
        return { success: true, url }
    } catch (error) {}
}

export async function deleteFile(key: string) {
    const params: DeleteObjectCommandInput = {
        Bucket: BUCKET,
        Key: key
    }

    try {
        await s3Client.send(new DeleteObjectCommand(params))
        return { success: true }
    } catch (error) {
        console.error('S3 Delete Error:', error)
        return { success: false, error }
    }
}

export async function listFiles(prefix?: string, maxKeys = 100) {
    const params: ListObjectsV2CommandInput = {
        Bucket: BUCKET,
        Prefix: prefix,
        MaxKeys: maxKeys
    }

    try {
        const response = await s3Client.send(new ListObjectsV2Command(params))
        return {
            success: true,
            files:
                response.Contents?.map(obj => ({
                    key: obj.Key,
                    lastModified: obj.LastModified,
                    size: obj.Size
                })) || []
        }
    } catch (error) {
        console.error('S3 List Files Error:', error)
        return { success: false, error }
    }
}

export async function removeFilesFromS3(files: string[]) {
    console.log('Removing files from S3:', files)
    for (const file of files) {
        console.log('Deleting file:', file)
        const response = await deleteFile(file)
        if (!response.success) {
            throw new Error('Failed to delete file from S3')
        }
    }
}

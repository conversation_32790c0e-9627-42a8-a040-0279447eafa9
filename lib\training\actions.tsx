import 'server-only'
import React from 'react'
import { createAI, getMutableAIState, getAIState } from 'ai/rsc'
import { openai } from '@ai-sdk/openai'

import { BotMessage, BotErrorMessage } from '@/components/stocks'
import { nanoid } from '@/lib/utils/utils'
import { checkMessagesAccess } from '@/app/[locale]/actions'
import { BotVoiceMessage, UserMessage } from '@/components/stocks/message'
import { auth } from '@/auth'
import axios from 'axios'
import fs from 'fs'
import path from 'path'
import { cookies } from 'next/headers'
import { logger } from '@/logger/logger'
import ReactivateSubmitUserMessageButton from '@/components/buttons/reactivate-submit-button'
import { getExercise } from '../exams/actions'
import { trackActivity } from '../activity/activity-tracker'
import { ActivityTypes } from '../activity/types'
import { convertPdfBase64ToPngBase64 } from '../server-utils/server-utils'
import { getTranslations } from 'next-intl/server'
import { DesmosSchema, llmOutputRecipe } from '../desmos/types'
import { getPart } from '../parts/action'
import {
    PromptFeatureType,
    SystemPromptService
} from '../ai/prompts/system-prompt-service'
import { ExercisePrompts } from '../ai/prompts/exercise/types'
import { buildPrompt } from '../ai/prompts/prompt-builder'
import { FormattingPrompts } from '../ai/prompts/other/formatting-rules'
import { buildConditionalPrompt } from '../ai/prompts/chat/chat-prompts'
import { generateWithAi } from '../ai/llm/generator'
import { streamUIGeneric, UpdateAiStateParams } from '../ai/llm/streamer' // Import generic function and type
import { CoreMessage } from 'ai' // Import CoreMessage if needed for typing
import { fetchProviderAndModelByTask } from '../ai/llm/server'
import { registry } from '../ai/config-ai'
import { fetchTasksWithCompetenciesAndExamplesByDifficulty } from '../praxeo/server'
import { z } from 'zod'
import type { TypeDeTacheType } from '../types/praxeo.type'
import Handlebars from '../utils/handlebars.utils'
import { getEvaluationPromptsByLevelIdAndDomainId } from '../evaluation-prompts/actions'
import type { EvaluationPromptPartial } from '@/prisma/generated/zod/modelSchema/EvaluationPromptSchema'
import type { Domain } from '@/prisma/generated/zod/modelSchema/DomainSchema'
import type { Level } from '@/prisma/generated/zod/modelSchema/LevelSchema'
import { EvaluationTypeType } from '@/prisma/generated/zod/inputTypeSchemas/EvaluationTypeSchema'

const documentOcr = process.env.DOCUMENT_OCR ?? 'mistral'

let fileBase64: string | undefined = undefined

// Define the state update logic specific to Training AI State
// Assuming the message structure is similar enough to Chat's AIStateMessage
// If TrainAIState has a different message structure, adjust this accordingly.
function updateTrainingAiStateOnDone(params: UpdateAiStateParams): AIState {
    // Using AIState type from training
    const { currentState, newContent, newAudio } = params
    const messages = currentState.messages || []
    // Use the structure defined within TrainAI's AIState type for messages
    const newMessage = {
        id: nanoid(),
        role: 'assistant',
        content: newContent,
        createdAt: new Date(),
        ...(newAudio && {
            transcribedAudio: newContent, // Assuming content is the transcription for AI voice
            audioFile: {
                id: nanoid(),
                name: 'ai-audio-' + nanoid(4),
                audioFile: newAudio,
                createdAt: new Date()
            }
        })
    }

    logger.debug(
        `[updateTrainingAiStateOnDone] New assistant message: ${JSON.stringify({ ...newMessage, audioFile: newAudio ? '...' : undefined })}`
    )

    return {
        ...currentState,
        messages: [...messages, newMessage]
    }
}

async function submitUserMessage(
    content: string,
    exercise: string,
    fileUrl: string,
    fileExtension: string,
    transcriptionResult: string,
    base64RecordedAudio: string
) {
    'use server'
    const cookieStore = await cookies()
    const session = await auth()

    const resultAccess = await checkMessagesAccess() // Renamed variable

    if (resultAccess?.type === 'error') {
        // Handle error case
        return {
            id: nanoid(),
            display: <BotErrorMessage content={resultAccess.message} />
        }
    }

    const t = await getTranslations('lib.prompts')
    const tc = await getTranslations('lib.errors')

    logger.debug(`[SubmitMessage (training mode)] Using language: ${t('lang')}`)

    const topic = cookieStore.get('topic')?.value

    const mode = cookieStore.get('mode')?.value

    const aiState = getMutableAIState<typeof TrainAI>() // Use TrainAI here

    // Get the length first
    const totalErrors = Number.parseInt(tc('chat.errorTexts.length'))
    // Then use it for random selection
    const randomIndex = Math.floor(Math.random() * totalErrors) + 1
    const randomErrorText = tc(`chat.errorTexts.${randomIndex}`)

    logger.debug(`[SubmitMessage] Random error text: ${randomErrorText}`)

    let mathpixOcrText = ''
    let finalPromptToGPT = ''

    if (fileUrl) {
        // Send the image file to the Mathpix OCR service and get the response
        const mathpixOcrResponse =
            documentOcr === 'mistral'
                ? await sendFileToMistralOcr(fileUrl, fileExtension)
                : await sendFileToMathpixOcr(fileUrl, fileExtension)

        mathpixOcrText = mathpixOcrResponse.text
    }

    // Construct the final prompt
    if (mathpixOcrText) {
        if (!exercise) {
            finalPromptToGPT = `msgllm4 Voilà le contenu de la copie, image ou just contenu à corriger :\n\n ${mathpixOcrText} \n \n ${transcriptionResult} \n Ceci est la fin du document.${content} `
        }
        if (exercise) {
            finalPromptToGPT = `msgllm4 Voilà la réponse de l'utilisateur à corriger :\n\n${mathpixOcrText}\n \n ${transcriptionResult} \n Ceci est la fin du document.${content} `
        }
    }

    if (!mathpixOcrText) {
        if (!exercise) {
            finalPromptToGPT = `msgllm4 \n \n ${transcriptionResult} \n Ceci est la fin du document.${content} `
        }
        if (exercise) {
            finalPromptToGPT = `msgllm4 Voilà la réponse de l'utilisateur à corriger : \n ${transcriptionResult} \n Ceci est la fin du document.${content} `
        }
    }

    const chatPrompt = (await SystemPromptService.getSystemPrompt(
        PromptFeatureType.CHAT
    )) as import('../ai/prompts/chat/types').ChatPrompts

    const conditionalPrompt = await buildConditionalPrompt(
        'Exam', // Hardcoded "Exam" feature? Review if this is correct.
        base64RecordedAudio ? transcriptionResult : undefined,
        topic,
        exercise
    )

    try {
        aiState.update({
            ...aiState.get(),
            messages: [
                ...aiState.get().messages,
                {
                    id: nanoid(),
                    role: 'user',
                    content: finalPromptToGPT || content,
                    transcribedAudio: transcriptionResult,
                    file: fileUrl
                        ? {
                              id: nanoid(),
                              name: fileUrl,
                              file: fileBase64
                          }
                        : undefined,
                    audioFile: base64RecordedAudio
                        ? {
                              id: nanoid(),
                              name: nanoid(),
                              audioFile: base64RecordedAudio
                          }
                        : undefined,
                    createdAt: new Date()
                }
            ]
        })

        // --- Use the generic streamer ---
        const currentLLM = getCurrentLLM(mode || 'GPT 4-o') // Re-added getCurrentLLM call locally
        let actualModel = openai(currentLLM)
        const providerAndModel =
            await fetchProviderAndModelByTask(ELEVE_EXERCICE)

        if (providerAndModel.profile?.name && providerAndModel.model) {
            const reg = await registry()
            actualModel = reg.languageModel(
                `${providerAndModel.profile.name}:${providerAndModel.model}`
            )
        }

        const systemPromptContent = // Renamed variable
            getSubjectPrompt(topic || 'Mathématiques') + // Keep subject prompt logic
            `\n réponds en utilisant la langue de l'utilisateur: ${t('lang')} \n` +
            chatPrompt.mainPrompt.prompt +
            conditionalPrompt

        logger.debug(`System prompt: ${systemPromptContent}`)

        // Cast messages to the expected type for streamUIGeneric
        const messagesForStreamer = aiState.get().messages as Array<{
            role: CoreMessage['role']
            content: CoreMessage['content']
            [key: string]: any
        }>

        const resultStream = await streamUIGeneric({
            // Renamed variable
            model: actualModel,
            systemPrompt: systemPromptContent,
            messages: messagesForStreamer,
            aiStateUpdater: aiState, // Pass the TrainAI state updater
            isVoiceResponse: !!base64RecordedAudio, // Determine voice based on base64RecordedAudio
            updateAiStateOnDoneCallback: updateTrainingAiStateOnDone // Use the training-specific callback
            // No specific content transformation needed here based on original code
        })
        // --- End generic streamer usage ---

        if (session?.user?.id) {
            trackActivity(session?.user?.id, ActivityTypes.MESSAGE_CREATED)

            if (fileUrl) {
                trackActivity(session?.user?.id, ActivityTypes.FILE_UPLOADED)
            }
        }

        return {
            id: nanoid(),
            display: resultStream.value // Use the result from the generic streamer
        }
    } catch (error) {
        console.log(
            'The error occuring in chat actions is : ' +
                error +
                JSON.stringify(error, null, 2)
        )
        return {
            id: nanoid(),
            display: <BotErrorMessage content={randomErrorText} />
        }
    }
}

export type Message = {
    role: 'user' | 'assistant' | 'system' | 'function' | 'data' | 'tool'
    content: string
    transcribedAudio?: string | null
    id?: string
    name?: string | null
    file?: {
        id: string
        name: string
        file: string | undefined
        createdAt?: Date | undefined
    }
    audioFile?: {
        id: string
        name: string
        audioFile: string | undefined
        createdAt?: Date | undefined
    }
    createdAt?: Date | undefined
}

export type AIState = {
    chatId: string
    topic: string
    messages: {
        role: 'user' | 'assistant' | 'system' | 'function' | 'data' | 'tool'
        content: string
        transcribedAudio?: string | null
        id: string
        name?: string | null
        file?: {
            id: string
            name: string
            file: string | undefined
            createdAt?: Date | undefined
        }
        audioFile?: {
            id: string
            name: string
            audioFile: string | undefined
            createdAt?: Date | undefined
        }
        createdAt?: Date | undefined
    }[]
    exerciseId?: string | null
    saved: boolean
    chatPath?: string
}

export type UIState = {
    id: string
    display: React.ReactNode
}[]

export const TrainAI = createAI<AIState, UIState>({
    actions: {
        submitUserMessage
    },
    initialUIState: [],
    initialAIState: {
        chatId: '',
        topic: '',
        messages: [],
        saved: false,
        chatPath: '',
        exerciseId: null
    },
    onGetUIState: async () => {
        'use server'
        const session = await auth()
        if (session && session.user) {
            const aiState = getAIState()
            if (aiState) {
                //@ts-expect-error Explanation: argument of tye read-only is not assignable to argument of type aiState
                const uiState = getUIStateFromAIState(aiState)
                return uiState
            }
        } else {
            return
        }
    },
    onSetAIState: async ({
        state,
        done
    }: {
        state: AIState
        done: boolean
    }) => {
        'use server'
        const session = await auth()
        if (session && session.user && !state.saved && done) {
            state.saved = false
        } else {
            return
        }
    }
})

export const getUIStateFromAIState = (aiState: AIState) => {
    return aiState.messages
        .filter(message => message.role !== 'system')
        .map((message, index) => ({
            id: `${aiState.chatId}-${index}`,
            display:
                message.role === 'user' ? (
                    <UserMessage
                        transcribedAudio={message.transcribedAudio || ''}
                        audio={message.audioFile?.audioFile || ''}
                        file={message.file}
                    >
                        {message.content.startsWith('msgllm4')
                            ? message.content.substring(
                                  message.content.indexOf(
                                      'Ceci est la fin du document.'
                                  ) + 'Ceci est la fin du document.'.length
                              )
                            : message.content}
                    </UserMessage>
                ) : (
                    <>
                        {message.transcribedAudio ? (
                            <>
                                <BotVoiceMessage
                                    transcribedAudio={message.transcribedAudio}
                                    aiAudio={message.audioFile?.audioFile}
                                />
                                <ReactivateSubmitUserMessageButton />
                            </>
                        ) : (
                            <BotMessage content={message.content} />
                        )}
                    </>
                )
        }))
}

export async function sendFileToMathpixOcr(
    fileName: string,
    fileExtension: string,
    fileData?: string
): Promise<{ text: string }> {
    try {
        let fileBuffer: Buffer | undefined = undefined
        let filePath: string | undefined = undefined

        if (!fileData) {
            //sendingToMathpix = true
            const uploadDir = path.join(process.cwd(), 'public', 'uploads')
            filePath = path.join(uploadDir, fileName)
            if (!fs.existsSync(filePath)) {
                throw new Error('File not found')
            }
            fileBuffer = fs.readFileSync(filePath)
        } else {
            fileBuffer = Buffer.from(fileData, 'base64')
        }

        if (fileExtension === 'pdf') {
            const bodyFormData = new FormData()

            const fileBlob = new Blob([fileBuffer], { type: 'application/pdf' })

            bodyFormData.append('file', fileBlob)

            // This endpoint will launch an asynchronous process
            const response = await axios.post(
                'https://api.mathpix.com/v3/pdf',
                bodyFormData,
                {
                    headers: {
                        app_key: process.env.MATHPIX_API_KEY
                    }
                }
            )

            //console.log(response.data)

            const pdf_id = response.data.pdf_id

            // Send a GET request to check if the process is done or not, needs to be a loop until the process is done, also wait 1 second before
            let done = false
            let text = ''

            while (!done) {
                await new Promise(resolve => setTimeout(resolve, 1000))
                const response = await axios.get(
                    `https://api.mathpix.com/v3/pdf/${pdf_id}`,
                    {
                        headers: {
                            app_key: process.env.MATHPIX_API_KEY
                        }
                    }
                )
                //console.log(response.data)

                done = response.data.status === 'completed'
            }

            // Final GET to get the result lines in json format
            const finalResponse = await axios.get(
                `https://api.mathpix.com/v3/pdf/${pdf_id}.lines.json`,
                {
                    headers: {
                        app_key: process.env.MATHPIX_API_KEY
                    }
                }
            )

            // response is a json object that has pages of lines so we need to concatenate the text only
            finalResponse.data.pages.forEach((page: any) => {
                page.lines.forEach((line: any) => {
                    text += line.text + '\n'
                })
            })

            fileBase64 = `data:application/pdf;base64,${fileBuffer.toString('base64')}`

            if (filePath) {
                fs.unlinkSync(filePath)
            }

            return { text: text }
        } else {
            const base64File = fileBuffer.toString('base64')

            // For future references, PDF files in base64 start like this : "data:application/pdf;base64,"

            fileBase64 = `data:image;base64,${base64File}`

            const response = await axios.post(
                'https://eu-central-1.api.mathpix.com/v3/text',
                {
                    src: `data:image;base64,${base64File}`
                },
                {
                    headers: {
                        'Content-Type': 'application/json',
                        app_key: process.env.MATHPIX_API_KEY
                        // 'app_id': 'MATHPIX_APP_ID',
                    }
                }
            )

            //sendingToMathpix = false

            if (filePath) {
                fs.unlinkSync(filePath)
            }

            return { text: response.data.text }
        }
    } catch {
        //sendingToMathpix = false
        return { text: '' }
    }
}

export async function sendFileToMistralOcr(
    fileName: string,
    fileExtension: string,
    fileData?: string
): Promise<{ text: string }> {
    try {
        let fileBuffer: Buffer | undefined = undefined
        let filePath: string | undefined = undefined

        if (!fileData) {
            const uploadDir = path.join(process.cwd(), 'public', 'uploads')
            filePath = path.join(uploadDir, fileName)

            if (!fs.existsSync(filePath)) {
                throw new Error('File not found')
            }

            fileBuffer = fs.readFileSync(filePath)
        } else {
            fileBuffer = Buffer.from(fileData, 'base64')
        }

        const fileBase64 = `data:application/${fileExtension};base64,${fileBuffer.toString('base64')}`

        const MISTRAL_API_KEY = process.env.MISTRAL_API_KEY
        const payload = {
            model: 'mistral-ocr-latest',
            document: {
                type: 'document_url',
                document_url: fileBase64
            },
            include_image_base64: true
        }

        const response = await axios.post(
            'https://api.mistral.ai/v1/ocr',
            payload,
            {
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${MISTRAL_API_KEY}`
                }
            }
        )

        if (filePath) {
            fs.unlinkSync(filePath)
        }

        if (response.data && response.data.pages) {
            const ocrText = response.data.pages
                .map((page: any) => page.markdown)
                .join('\n\n')
            return { text: ocrText }
        }

        return { text: '' }
    } catch (err) {
        console.error('Mistral OCR error:', err)
        logger.error('Error processing file with Mistral OCR:', err)
        return { text: '' }
    }
}

function getSubjectPrompt(subject: string): string {
    switch (subject.toLowerCase()) {
        case 'mathématiques':
            return `Tu es un expert en mathématiques.`
        case 'physique-chimie':
            return `Tu es un expert en Physique et en Chimie.`
        case 'svt':
            return 'Tu es un expert en Sciences de la vie et de la terre.'
        case 'autre':
            return "Tu es un enseignant doté de compétences pluridisciplinaires, c'est à dire toutes les matières enseignés à l'école (du primaire à l'université)."
        default:
            return `Tu es un enseignant doté de compétences pluridisciplinaires, c'est à dire toutes les matières enseignés à l'école (du primaire à l'université).`
    }
}

// Need to keep this local version or import from generator.ts if it's intended to be shared
function getCurrentLLM(llm: string): string {
    switch (llm) {
        case 'GPT 3.5 Turbo':
            return `gpt-3.5-turbo`
        case 'GPT 4':
            return 'gpt-4'
        case 'GPT 4 Turbo':
            return `gpt-4-turbo`
        case 'GPT 4-o':
            return 'gpt-4o'
        case 'GPT 4-o Mini':
            return 'gpt-4o-mini'
        default:
            return `gpt-3.5-turbo`
    }
}

export interface TrainingModeGeneratorInput {
    // questionId?: string, // TODO: remove this
    partId?: string
    questionTitle?: string
    exoId?: string
    difficulty: 0 | 1 | 2 | 3
    variation: 'moindre' | 'moyenne' | 'élevée' | 'très élevée'
    numberOfExercises: number
    llmModel?: string
    file?: {
        name?: string
        extension?: string
        data: string
    }
    solutionFile?: {
        name?: string
        extension?: string
        data: string
    }
    customPrompt?: string
    lastResult?: string
    chapterIds?: string[]
    domain?: Domain
    level?: Level
}

export interface TrainingModeGeneratorOutput {
    questionContent: string
    desmosCode?: z.infer<typeof DesmosSchema>
    contentType: 'text' | 'html' | 'latex'
}

// function to print input without file data:
const printInput = async (input: TrainingModeGeneratorInput) => {
    // Create a copy of the input object
    const inputCopy = { ...input }

    // Remove file and solutionFile data
    if (inputCopy.file) {
        inputCopy.file = { ...inputCopy.file, data: '[File data omitted]' }
    }
    if (inputCopy.solutionFile) {
        inputCopy.solutionFile = {
            ...inputCopy.solutionFile,
            data: '[Solution file data omitted]'
        }
    }

    // Convert the object to a formatted JSON string
    const formattedInput = JSON.stringify(inputCopy, null, 2)

    // Log the formatted input
    logger.debug(`Training mode input : ${formattedInput}`)
}

/**
 * Generates training mode exercises based on the given input.
 *
 * @param input - The input for generating training mode exercises.
 * @returns A promise that resolves to an array of training mode exercises or null if generation fails.
 */
const getPraxeoTasks = async (
    input: TrainingModeGeneratorInput
): Promise<{
    domain?: string
    level?: string
    questionTitle?: string
    tasksType: TypeDeTacheType[] | string
    questionDifficulty: string
    questionContentType: string
    solutionContent?: string
    lastResult?: string
    examFile?: string
    chapter?: string
}> => {
    if (input.partId) {
        const part = await getPart(input.partId)
        const domainName = part?.chapter?.domain?.name
        const levelName = part?.chapter?.level?.name
        const tasks = await fetchTasksWithCompetenciesAndExamplesByDifficulty(
            input.partId,
            input.difficulty
        )

        if (!part) {
            logger.error('No part found')
            throw new Error(
                'Failed to get part for training mode exercises generation'
            )
        }

        return {
            level: levelName,
            domain: domainName,
            questionTitle: part.name || '',
            tasksType: tasks as TypeDeTacheType[],
            questionDifficulty: input.difficulty.toString(),
            questionContentType: 'latex',
            chapter: part.chapter?.title || ''
        }
    } else if (input.exoId) {
        const currentExo = await getExercise(input.exoId)
        if (!currentExo) {
            throw new Error(
                'Failed to get exercise for training mode exercises generation'
            )
        }
        return {
            domain: input.domain?.name,
            questionTitle: currentExo.title || '',
            tasksType: currentExo.assignment || '',
            questionDifficulty: input.difficulty.toString(),
            questionContentType: 'latex',
            solutionContent: currentExo.solution,
            examFile: currentExo.assignmentMedia
        }
    } else if (input.file) {
        logger.info('Sending File To OCR')
        if (!input.file.data) {
            throw new Error(
                'No file data provided for training mode exercises generation'
            )
        }
        const questionContent = (
            documentOcr === 'mistral'
                ? await sendFileToMistralOcr(
                      input.file.name || 'file',
                      input.file.extension || 'pdf',
                      input.file.data
                  )
                : await sendFileToMathpixOcr(
                      input.file.name || 'file',
                      input.file.extension || 'pdf',
                      input.file.data
                  )
        ).text
        const solutionContent = input.solutionFile
            ? (documentOcr === 'mistral'
                  ? await sendFileToMistralOcr(
                        input.solutionFile.name || 'file',
                        input.solutionFile.extension || 'pdf',
                        input.solutionFile.data
                    )
                  : await sendFileToMathpixOcr(
                        input.solutionFile.name || 'file',
                        input.solutionFile.extension || 'pdf',
                        input.solutionFile.data
                    )
              ).text
            : undefined
        return {
            domain: input.domain?.name,
            tasksType: questionContent,
            questionDifficulty: input.difficulty.toString(),
            questionContentType: 'latex',
            solutionContent
        }
    } else if (input.lastResult) {
        return {
            domain: input.domain?.name,
            tasksType: '',
            questionDifficulty: input.difficulty.toString(),
            questionContentType: 'json (stringify)',
            lastResult: input.lastResult
        }
    } else {
        throw new Error('Invalid input for exercise generation')
    }
}

const ELEVE_EXERCICE = 'Élève / Exercice'
// EXERCISE MODE, Generates exercises based on the input
const generateExercises = async (
    input: TrainingModeGeneratorInput
): Promise<TrainingModeGeneratorOutput[] | null> => {
    'use server'

    const session = await auth()

    const t = await getTranslations('lib.prompts')
    logger.debug(`[GENERATE_EXERCISES] lang : ${t(`lang`)}`)

    if (!session?.user) {
        throw new Error('User not logged in')
    }

    try {
        printInput(input)
        const {
            tasksType: questionContent,
            chapter,
            examFile,
            lastResult,
            questionTitle: partName
        } = await getPraxeoTasks(input)

        const regenerationInstructions = input.lastResult
            ? `Ceci est une régénération. Utilisez le résultat précédent comme référence, mais créez des exercices DIFFÉRENTS. Maintenez le même niveau de difficulté et le même type de contenu que les exercices originaux. Assurez-vous que les nouvelles questions sont uniques et non des copies des précédentes.

          Résultat précédent à modifier : ${lastResult}`
            : ''

        const exercisePrompt = (await SystemPromptService.getSystemPrompt(
            PromptFeatureType.EXERCISE
        )) as ExercisePrompts
        const formattingPrompt = (await SystemPromptService.getSystemPrompt(
            PromptFeatureType.FORMATTING
        )) as FormattingPrompts
        const prompts = (await getEvaluationPromptsByLevelIdAndDomainId(
            input.level?.id,
            input.domain?.id
        )) as EvaluationPromptPartial[]
        const modalites: Record<number, EvaluationTypeType> = {
            0: 'DIAGNOSTIC',
            1: 'FORMATIVE',
            2: 'SUMMATIVE',
            3: 'CERTIFYING'
        }
        const modalitePrompt = prompts.find(
            p => p.title === modalites[input.difficulty]
        )?.prompt
        const mainPrompt = lastResult
            ? exercisePrompt.regenerateExamPromptRules.prompt
            : input.partId
              ? exercisePrompt.trainingModePromptRules.prompt
              : exercisePrompt.fileToKatexPromptRules.prompt

        const builtPrompt = await buildPrompt({
            customPrompt: mainPrompt,
            customkatexRules: formattingPrompt.katexRules.prompt,
            customMarkdownRules: formattingPrompt.markdownRules.prompt,
            needsDesmos: true,
            needsMath: true,
            needsChemistry: true
        })
        console.log('🚀 ~ builtPrompt:', builtPrompt)

        const processPrompt = Handlebars.compile(builtPrompt)
        // const processedPrompt = Mustache.render(
        //     builtPrompt,
        //     {
        //         selectedTask: questionContent,
        //         domain: input.domain,
        //         level: input.level,
        //         part: partName,
        //         chapter: chapter,
        //         exerciseCount: input.numberOfExercises,
        //         difficultyLevel: input.difficulty,
        //         customPrompt: input.customPrompt,
        //         regenerationInstructions,
        //         questionContent: questionContent
        //     },
        //     {},
        //     ['{', '}']
        // )

        const currentLLM = getCurrentLLM(
            input.llmModel ||
                (session?.user?.isAdmin || session?.user?.subscribed
                    ? 'GPT 4-o'
                    : 'GPT 4-o Mini')
        )

        const providerAndModel =
            await fetchProviderAndModelByTask(ELEVE_EXERCICE)

        logger.debug(
            'Current LLM for training mode exercises generation: ' + currentLLM
        )

        const pngBase64String = await convertPdfBase64ToPngBase64(
            input.file?.data || examFile || ''
        )
        logger.info('question content' + questionContent)

        logger.info(
            'prompt after process:' +
                processPrompt({
                    selectedTask: questionContent,
                    domain: input.domain,
                    level: input.level,
                    part: partName,
                    chapter: chapter,
                    exerciseCount: input.numberOfExercises,
                    difficultyLevel: input.difficulty,
                    customPrompt: input.customPrompt,
                    regenerationInstructions,
                    questionContent: questionContent
                })
        )

        const exercises = await generateWithAi({
            user: session?.user,
            systemPrompt: `IMPORTANT: Make sure that the content generated is in this language : ${t(`lang`)}`,
            prompt: processPrompt({
                selectedTask: questionContent,
                domain: input.domain,
                level: input.level,
                part: partName,
                chapter: chapter,
                modalite: modalitePrompt,
                exerciseCount: input.numberOfExercises,
                difficultyLevel: input.difficulty,
                customPrompt: input.customPrompt,
                regenerationInstructions,
                questionContent: questionContent
            }),
            llmOutputRecipe: llmOutputRecipe,
            pngs: pngBase64String ? [pngBase64String] : undefined,
            feature: PromptFeatureType.EXERCISE,
            functionName: 'generateExercises',
            providerName: providerAndModel.profile?.name,
            modelName: providerAndModel.model
        })

        if (!exercises) {
            logger.error('Failed to generate exercises')
            throw new Error('Failed to generate exercises')
        }

        return exercises?.output as TrainingModeGeneratorOutput[]
    } catch (error) {
        logger.error('Failed to generate or parse exercises:' + error)
        throw new Error('Failed to generate or parse exercises')
    }
}

export const generateTrainingModeExercises = generateExercises
export const regenerateTrainingModeExercises = generateExercises

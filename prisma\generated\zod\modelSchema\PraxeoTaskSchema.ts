import { z } from 'zod';
import { JsonValueSchema } from '../inputTypeSchemas/JsonValueSchema'
import type { JsonValueType } from '../inputTypeSchemas/JsonValueSchema';
import { PraxeoTaskSkillWithRelationsSchema, PraxeoTaskSkillPartialWithRelationsSchema, PraxeoTaskSkillOptionalDefaultsWithRelationsSchema } from './PraxeoTaskSkillSchema'
import type { PraxeoTaskSkillWithRelations, PraxeoTaskSkillPartialWithRelations, PraxeoTaskSkillOptionalDefaultsWithRelations } from './PraxeoTaskSkillSchema'
import { PartWithRelationsSchema, PartPartialWithRelationsSchema, PartOptionalDefaultsWithRelationsSchema } from './PartSchema'
import type { PartWithRelations, PartPartialWithRelations, PartOptionalDefaultsWithRelations } from './PartSchema'

/////////////////////////////////////////
// PRAXEO TASK SCHEMA
/////////////////////////////////////////

export const PraxeoTaskSchema = z.object({
  id: z.number(),
  description: z.string(),
  examples: JsonValueSchema.nullable(),
  isDisabled: z.boolean(),
  tmPartId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PraxeoTask = z.infer<typeof PraxeoTaskSchema>

/////////////////////////////////////////
// PRAXEO TASK PARTIAL SCHEMA
/////////////////////////////////////////

export const PraxeoTaskPartialSchema = PraxeoTaskSchema.partial()

export type PraxeoTaskPartial = z.infer<typeof PraxeoTaskPartialSchema>

/////////////////////////////////////////
// PRAXEO TASK OPTIONAL DEFAULTS SCHEMA
/////////////////////////////////////////

export const PraxeoTaskOptionalDefaultsSchema = PraxeoTaskSchema.merge(z.object({
  id: z.number().optional(),
  isDisabled: z.boolean().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type PraxeoTaskOptionalDefaults = z.infer<typeof PraxeoTaskOptionalDefaultsSchema>

/////////////////////////////////////////
// PRAXEO TASK RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoTaskRelations = {
  praxeoTaskSkills: PraxeoTaskSkillWithRelations[];
  tmPart: PartWithRelations;
};

export type PraxeoTaskWithRelations = Omit<z.infer<typeof PraxeoTaskSchema>, "examples"> & {
  examples?: JsonValueType | null;
} & PraxeoTaskRelations

export const PraxeoTaskWithRelationsSchema: z.ZodType<PraxeoTaskWithRelations> = PraxeoTaskSchema.merge(z.object({
  praxeoTaskSkills: z.lazy(() => PraxeoTaskSkillWithRelationsSchema).array(),
  tmPart: z.lazy(() => PartWithRelationsSchema),
}))

/////////////////////////////////////////
// PRAXEO TASK OPTIONAL DEFAULTS RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoTaskOptionalDefaultsRelations = {
  praxeoTaskSkills: PraxeoTaskSkillOptionalDefaultsWithRelations[];
  tmPart: PartOptionalDefaultsWithRelations;
};

export type PraxeoTaskOptionalDefaultsWithRelations = Omit<z.infer<typeof PraxeoTaskOptionalDefaultsSchema>, "examples"> & {
  examples?: JsonValueType | null;
} & PraxeoTaskOptionalDefaultsRelations

export const PraxeoTaskOptionalDefaultsWithRelationsSchema: z.ZodType<PraxeoTaskOptionalDefaultsWithRelations> = PraxeoTaskOptionalDefaultsSchema.merge(z.object({
  praxeoTaskSkills: z.lazy(() => PraxeoTaskSkillOptionalDefaultsWithRelationsSchema).array(),
  tmPart: z.lazy(() => PartOptionalDefaultsWithRelationsSchema),
}))

/////////////////////////////////////////
// PRAXEO TASK PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoTaskPartialRelations = {
  praxeoTaskSkills?: PraxeoTaskSkillPartialWithRelations[];
  tmPart?: PartPartialWithRelations;
};

export type PraxeoTaskPartialWithRelations = Omit<z.infer<typeof PraxeoTaskPartialSchema>, "examples"> & {
  examples?: JsonValueType | null;
} & PraxeoTaskPartialRelations

export const PraxeoTaskPartialWithRelationsSchema: z.ZodType<PraxeoTaskPartialWithRelations> = PraxeoTaskPartialSchema.merge(z.object({
  praxeoTaskSkills: z.lazy(() => PraxeoTaskSkillPartialWithRelationsSchema).array(),
  tmPart: z.lazy(() => PartPartialWithRelationsSchema),
})).partial()

export type PraxeoTaskOptionalDefaultsWithPartialRelations = Omit<z.infer<typeof PraxeoTaskOptionalDefaultsSchema>, "examples"> & {
  examples?: JsonValueType | null;
} & PraxeoTaskPartialRelations

export const PraxeoTaskOptionalDefaultsWithPartialRelationsSchema: z.ZodType<PraxeoTaskOptionalDefaultsWithPartialRelations> = PraxeoTaskOptionalDefaultsSchema.merge(z.object({
  praxeoTaskSkills: z.lazy(() => PraxeoTaskSkillPartialWithRelationsSchema).array(),
  tmPart: z.lazy(() => PartPartialWithRelationsSchema),
}).partial())

export type PraxeoTaskWithPartialRelations = Omit<z.infer<typeof PraxeoTaskSchema>, "examples"> & {
  examples?: JsonValueType | null;
} & PraxeoTaskPartialRelations

export const PraxeoTaskWithPartialRelationsSchema: z.ZodType<PraxeoTaskWithPartialRelations> = PraxeoTaskSchema.merge(z.object({
  praxeoTaskSkills: z.lazy(() => PraxeoTaskSkillPartialWithRelationsSchema).array(),
  tmPart: z.lazy(() => PartPartialWithRelationsSchema),
}).partial())

export default PraxeoTaskSchema;

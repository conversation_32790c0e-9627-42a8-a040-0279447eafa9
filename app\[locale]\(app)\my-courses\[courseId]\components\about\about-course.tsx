import React, { useEffect, useState } from 'react'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Accordion } from '@/components/ui/accordion'
import { getThemesByClassId } from '@/lib/theme/actions'
import { Theme } from '@/prisma/generated/zod/modelSchema/ThemeSchema'
import CourseDetailsCard from './course-details-card'
import ThemeBlock from './theme-bloc'

type AboutCourseProps = {
    courseId?: string
    triggerHighlight: () => void
}

function AboutCourse({ courseId, triggerHighlight }: AboutCourseProps) {
    const [themes, setThemes] = useState<Theme[]>()
    useEffect(() => {
        ;(async () => {
            const themes = await getThemesByClassId(courseId!)
            setThemes(themes as Theme[])
        })()
    }, [])

    return (
        <div className="flex flex-col gap-2 w-full h-[calc(100vh-3.5rem)]">
            <CourseDetailsCard id={courseId} />
            {/* {themes && themes?.length > 0 && 
        <div className='mx-2 w-full flex justify-end gap-2'>
        <IconButton icon={<Download />} rounded='rounded-sm' height='h-8' width='w-8' className='bg-dinoBotLightGray text-dinoBotBlackBlue hover:bg-dinoBotLightGray' onClick={()=>{
        }} />
        <IconButton icon={<Printer />} rounded='rounded-sm' height='h-8' width='w-8' className='bg-dinoBotLightGray text-dinoBotBlackBlue hover:bg-dinoBotLightGray' />
      </div> } */}
            <ScrollArea className="flex flex-col gap-2 ml-2 w-full h-[38%] pr-3">
                <Accordion
                    type="single"
                    collapsible
                    className="flex flex-col gap-2 w-full"
                >
                    {themes?.map(theme => (
                        <ThemeBlock
                            key={theme.id}
                            theme={theme}
                            triggerHighlight={triggerHighlight}
                        />
                    ))}
                </Accordion>
            </ScrollArea>
        </div>
    )
}

export default AboutCourse

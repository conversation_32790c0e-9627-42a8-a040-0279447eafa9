'server-only'

import { UserStatus } from '@prisma/client'

// Récupérer le nombre de jours depuis la variable d'environnement ou utiliser 30 par défaut
const ACCOUNT_DELETION_DAYS = parseInt(
    process.env.ACCOUNT_DELETION_DAYS || '30',
    10
)

export function disableAccount(email: string) {
    return prisma?.user.update({
        where: { email },
        data: {
            status: UserStatus.PENDING_DELETION,
            // Set scheduledDeletionAt to ACCOUNT_DELETION_DAYS days from now
            scheduledDeletionAt: new Date(
                Date.now() + ACCOUNT_DELETION_DAYS * 24 * 60 * 60 * 1000
            )
        }
    })
}

export function enableAccount(email: string) {
    return prisma?.user.update({
        where: { email },
        data: { status: UserStatus.ACTIVE, scheduledDeletionAt: null }
    })
}

export async function getDeletionDate(email: string) {
    return prisma?.user.findUnique({
        where: { email },
        select: { scheduledDeletionAt: true }
    })
}

/*
  Warnings:

  - A unique constraint covering the columns `[rang]` on the table `feature_flags` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "feature_flags" ADD COLUMN     "rang" INTEGER;

-- CreateIndex
CREATE UNIQUE INDEX "feature_flags_rang_key" ON "feature_flags"("rang");

-- Update description for STUDENT_EXERCISE_MODE
UPDATE "feature_flags"
SET "description" = 'Génération d''exercices contextuels adaptés au niveau et au programme de chaque élève. Impact : Les élèves peuvent s''entraîner avec des exercices personnalisés générés automatiquement selon leurs besoins.'
WHERE "feature_name" = 'STUDENT_EXERCISE_MODE';

-- Set order for feature flags
UPDATE "feature_flags" SET "rang" = 1 WHERE "feature_name" = 'STUDENT_CHAT_MODE';
UPDATE "feature_flags" SET "rang" = 2 WHERE "feature_name" = 'STUDENT_EXAM_MODE';
UPDATE "feature_flags" SET "rang" = 3 WHERE "feature_name" = 'STUDENT_EVALUATION_MODE';
UPDATE "feature_flags" SET "rang" = 4 WHERE "feature_name" = 'STUDENT_EXERCISE_MODE';
UPDATE "feature_flags" SET "rang" = 5 WHERE "feature_name" = 'STUDENT_CLASSES_VIEW';

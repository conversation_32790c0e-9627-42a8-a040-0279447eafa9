-- CreateEnum
CREATE TYPE "FeatureFlagName" AS ENUM ('STUDENT_CLASSES_VIEW', 'STUDENT_CHAT_MODE', 'STUDENT_EXERCISE_MODE', 'STUDENT_EVALUATION_MODE', 'STUDENT_EXAM_MODE');

-- CreateTable
CREATE TABLE "feature_flags" (
    "id" TEXT NOT NULL DEFAULT gen_random_uuid(),
    "feature_name" "FeatureFlagName" NOT NULL,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "feature_flags_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "feature_flags_feature_name_key" ON "feature_flags"("feature_name");

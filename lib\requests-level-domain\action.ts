'use server'
import {
    fetchRequestsLevelDomainByUserID,
    addRequestLevelDomain
} from './server'
import { errorHandleLogger, withErrorHandling } from '../hof/error-handling'
import { withUserAuth } from '../hof/auth'

/**
 * Get all requests level domain for the authenticated user
 * @returns Array of requests level domain
 * @throws {UnauthorizedError} if the user is not authenticated
 * @throws {Error} if there is an error
 */
export const getRequestsLevelDomain = withErrorHandling(
    withUserAuth(fetchRequestsLevelDomainByUserID),
    errorHandleLogger
)

/**
 * Create a new request level domain for the authenticated user
 * @param explanation - The explanation for the request
 * @param levelDomainIds - Array of level domain IDs
 * @returns The created request
 * @throws {UnauthorizedError} if the user is not authenticated
 * @throws {Error} if there is an error
 */
export const createRequestLevelDomain = withErrorHandling(
    with<PERSON><PERSON><PERSON><PERSON>(addRequestLevelDomain),
    errorHandleLogger
)

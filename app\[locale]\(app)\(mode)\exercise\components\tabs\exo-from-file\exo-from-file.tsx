//Hooks
import React, { useEffect } from 'react'
import { useTranslations } from 'next-intl'
import { useRouter } from '@/i18n/routing'

//UI
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { toast } from 'sonner'
import { Slider } from '@/components/ui/slider'

//Icons
import { CircleCheckBig, Upload } from 'lucide-react'

//Store
import {
    CustomFile,
    selectUseExoModeStore
} from '@/lib/stores/exercise-mode-store/exercise-store'

function ExoFromFile() {
    const data = selectUseExoModeStore.use.exoInfoFromFile()
    const setData = selectUseExoModeStore.use.updateExoInfoFromFile()
    const setMode = selectUseExoModeStore.use.setMode()
    const router = useRouter()
    const t = useTranslations('app.mode.exo.tab.file')

    function convertToBase64(file: File): Promise<CustomFile> {
        return new Promise((resolve, reject) => {
            const fileReader = new FileReader()

            fileReader.onload = function (fileLoadedEvent) {
                const base64 = fileLoadedEvent.target?.result
                const result: CustomFile = {
                    extension: file.name.split('.').pop() || '',
                    name: file.name,
                    data: base64?.toString().split(',')[1] ?? ''
                }

                resolve(result)
            }

            fileReader.onerror = function (error) {
                reject(error)
            }

            fileReader.readAsDataURL(file)
        })
    }

    const handleFormChange = async (
        field: keyof typeof data,
        value: File | number | string
    ) => {
        let new_value: CustomFile

        if (value instanceof File) {
            new_value = await convertToBase64(value)
            console.log(new_value)
            setData(field, new_value)
            return
        }
        setData(field, value)
    }

    useEffect(() => {
        setMode('FROM_FILE')
    }, [])

    const handleSubmit = () => {
        if (data.exo) {
            setMode('FROM_FILE')
            router.push('/train')
        } else {
            toast.error(t('error'))
        }
    }

    return (
        <div className="flex flex-col gap-4 w-full h-fit">
            <div className="flex flex-col gap-9 flex-grow">
                <div className="flex flex-col gap-2">
                    <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {t('generate')}
                    </div>
                    <div className="w-fit flex flex-col gap-2 mt-1">
                        <div className="flex flex-col sm:flex-row gap-2 sm:gap-5">
                            <div>
                                <label
                                    htmlFor="exo-file"
                                    className="text-7 text-dinoBotGray"
                                >
                                    {t('enonce')}
                                </label>
                                <label
                                    htmlFor="exo-file"
                                    className={`flex items-center px-2 gap-1 w-60 h-10 text-[15px] font-semibold  text-dinoBotDarkGray rounded-md cursor-pointer hover:bg-dinoBotLightBlue transition-colors duration-200 ${data.exo ? 'bg-dinoBotGreen/20' : 'bg-dinoBotLightGray'}`}
                                >
                                    {data.exo ? (
                                        <CircleCheckBig className="size-4" />
                                    ) : (
                                        <Upload className="size-4" />
                                    )}
                                    <p>
                                        {data.exo ? t('replace') : t('upload')}{' '}
                                        <span className="text-dinoBotRed">
                                            *
                                        </span>
                                    </p>
                                </label>
                                <input
                                    className="hidden"
                                    type="file"
                                    name="exo-file"
                                    id="exo-file"
                                    accept="application/pdf"
                                    onChange={e => {
                                        if (e.target.files)
                                            handleFormChange(
                                                'exo',
                                                e.target.files[0]
                                            )
                                    }}
                                />
                            </div>
                            <div>
                                <label
                                    htmlFor="solution-file"
                                    className="text-7 text-dinoBotGray"
                                >
                                    {t('correction')}
                                </label>
                                <label
                                    htmlFor="solution-file"
                                    className={`flex items-center px-2 gap-1 w-60 h-10 text-[15px] font-semibold  text-dinoBotDarkGray rounded-md cursor-pointer hover:bg-dinoBotLightBlue transition-colors duration-200 ${data.solution ? 'bg-dinoBotGreen/20' : 'bg-dinoBotLightGray'}`}
                                >
                                    {data.solution ? (
                                        <CircleCheckBig className="size-4" />
                                    ) : (
                                        <Upload className="size-4" />
                                    )}
                                    <p>
                                        {data.solution
                                            ? t('replace')
                                            : t('upload')}{' '}
                                        <span className="text-dinoBotRed">
                                            *
                                        </span>
                                    </p>
                                </label>
                                <input
                                    className="hidden"
                                    type="file"
                                    name="exo-file"
                                    id="solution-file"
                                    accept="application/pdf"
                                    onChange={e => {
                                        if (e.target.files)
                                            handleFormChange(
                                                'solution',
                                                e.target.files[0]
                                            )
                                    }}
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div className="w-1/3 flex flex-col gap-3">
                    <div className="w-full">
                        <div className="flex gap-4 items-center text-sm font-bold text-dinoBotDarkGray">
                            <div className="flex gap-1 items-center whitespace-nowrap">
                                {t('difficulty')}{' '}
                                <span>{data.difficulty}/3</span>
                            </div>
                            <div className="flex-1">
                                <Slider
                                    defaultValue={[data.difficulty]}
                                    min={0}
                                    max={3}
                                    step={1}
                                    className={'w-full'}
                                    onValueChange={value =>
                                        handleFormChange('difficulty', value[0])
                                    }
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div className="flex flex-col gap-1">
                    <div className="flex gap-1 items-center text-sm font-bold text-dinoBotDarkGray">
                        {' '}
                        {t('prompt')}{' '}
                        <span className="font-normal">{t('opt')}</span>{' '}
                    </div>
                    <div className="mt-2">
                        <Textarea
                            placeholder={t('rid')}
                            value={data.customPrompt}
                            onChange={e => {
                                if (e.target.value.length < 1000)
                                    handleFormChange(
                                        'customPrompt',
                                        e.target.value
                                    )
                            }}
                        ></Textarea>
                        <p className="text-xs text-right mt-1 text-dinoBotGray">
                            {data.customPrompt ? data.customPrompt?.length : 0}
                            /1000 {t('caracteres')}
                        </p>
                    </div>
                </div>
            </div>
            <div className="w-full flex justify-center items-center mt-2">
                <Button
                    className="bg-dinoBotVibrantBlue hover:bg-dinoBotVibrantBlue/90 rounded-xl w-36"
                    onClick={handleSubmit}
                >
                    {t('submit')}
                </Button>
            </div>
        </div>
    )
}

export default ExoFromFile

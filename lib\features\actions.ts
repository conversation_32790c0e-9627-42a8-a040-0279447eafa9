'use server'
import { FeatureFlag, FeatureFlagName } from '@prisma/client'
import { withAuth } from '../hof/auth'
import { withErrorHandling } from '../hof/error-handling'
import * as server from './server'

export const getFeatureFlags = async () => {
    return withErrorHandling(withAuth(server.getFeatureFlags))() as Promise<
        FeatureFlag[]
    >
}

export const getFeaturesFlagsByName = async (name: FeatureFlagName) => {
    return withErrorHandling(withAuth(server.getFeaturesFlagsByName))(name)
}

export const getFeatureRoutesByFeatureName = async (name: FeatureFlagName) => {
    return withErrorHandling(withAuth(server.getFeatureRoutesByFeatureName))(
        name
    )
}

export const getFeaturesFlagsByNames = async (names: FeatureFlagName[]) => {
    return withErrorHandling(withAuth(server.getFeaturesFlagsByNames))(names)
}

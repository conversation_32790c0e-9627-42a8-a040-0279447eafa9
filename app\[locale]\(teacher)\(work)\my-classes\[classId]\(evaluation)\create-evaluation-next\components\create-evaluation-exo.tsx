'use client'
import { ScrollArea } from '@/components/ui/scroll-area'
import React, { useEffect, useState, useTransition } from 'react'
import ExoContent from './exo-content'
import { Eye, Loader2, Plus } from 'lucide-react'
import { Button } from '@/components/ui/button'
import DialogParamsEvaluation from './dialogs/dialog-params-evaluation'
import { useRouter } from '@/i18n/routing'
import { useEvaluationParamsStore } from '@/app/[locale]/(teacher)/(work)/my-classes/[classId]/(evaluation)/store/evaluation-params.store'
import {
    createControl,
    getControlByIdWithMedia,
    updateControl
} from '@/lib/control-mode/actions'
import DialogPreviewEval from '../../components/dialogs/dialog-preview-eval'
import { selectUseScheduleAssessmentStore } from '../../store/use-schedule-assessment-store'
import { useParams, useSearchParams } from 'next/navigation'
import { ControlWithPartialRelations } from '@/prisma/generated/zod/modelSchema/ControlSchema'
import { getDomainsByLevel } from '@/lib/domain-level/actions'
import { Level } from '@/prisma/generated/zod/modelSchema/LevelSchema'
import { isEmptyOrNull } from '@/lib/utils/string.utils'
import { useTranslations } from 'next-intl'
import ExoContentLoading from './exo-content-loading'
import { classEmit } from '@/lib/sse/class/class-events'

const CreateEvaluationExo = () => {
    const t = useTranslations('teacher.myClass.evaluation.next')
    const router = useRouter()
    const queryParam = useSearchParams()
    const evalId = queryParam.get('evalId')
    const params = useParams<{ classId: string }>()
    const {
        isOnLoadExo,
        addExo,
        havePossibilityToAdd,
        exos,
        domain,
        setLevelAndDomain,
        level,
        evalProps,
        setExos,
        setEvalProps,
        isUpdate,
        setUpdating,
        reset
    } = useEvaluationParamsStore()
    const setSelectedControl =
        selectUseScheduleAssessmentStore.use.setSelectedControl()
    const [isAdd, onAdd] = useTransition()
    const [isLoading, onLoading] = useTransition()
    const [loadId, setLoad] = useState<1 | 2 | 3>()
    useEffect(() => {
        if (isOnLoadExo && !evalId) router.push('create-evaluation')
        onLoading(async () => {
            try {
                if (evalId) {
                    setUpdating(true)
                    const controle = await getControlByIdWithMedia(evalId)
                    if ('error' in controle!) throw new Error()
                    const { exercises, ...rest } = controle!
                    const dataDomains = await getDomainsByLevel(
                        rest.assignedClass?.levelId
                    )
                    if ('error' in dataDomains) throw new Error()
                    setLevelAndDomain(
                        rest.assignedClass?.level as Level,
                        dataDomains.find(
                            domain => domain.name === rest.domainName
                        )!
                    )
                    setExos(exercises!)
                    setEvalProps(rest as ControlWithPartialRelations)
                }
            } catch (error) {
                console.error(error)
            }
        })
        return () => {
            reset()
        }
    }, [router])
    const saveExo = async (status: 'DRAFT' | 'ASSIGNED' | 'IN_PROGRESS') => {
        const { assignedClass, theme, submissions, ...rest } = evalProps || {}
        const data = { ...rest, exercises: exos, status }
        if (isUpdate) {
            const result = await updateControl(data.id!, data, level!, domain!)
            if (assignedClass?.id)
                await classEmit({
                    classIds: [assignedClass.id],
                    event: 'class:updated-evaluation',
                    data: theme?.classId
                })
            return result
        }
        return await createControl(data, level!, domain!)
    }
    const save = (
        status: 'DRAFT' | 'ASSIGNED' | 'IN_PROGRESS',
        loadId: 1 | 2
    ) => {
        setLoad(loadId)
        onAdd(() => {
            saveExo(status)
            reset()
            router.push(`/my-classes/${params.classId}`)
            setUpdating(false)
        })
    }
    const scheduleEvaluation = () => {
        setLoad(3)
        onAdd(async () => {
            setSelectedControl(await saveExo('IN_PROGRESS'))
            reset()
            setUpdating(false)
            router.push('schedule-assessment')
        })
    }
    return (
        <div>
            <div className="flex justify-between">
                <h2 className="font-medium text-lg">{t('title')}</h2>
                <div className="flex gap-4">
                    <DialogParamsEvaluation />
                    <DialogPreviewEval
                        exos={exos}
                        domain={domain!}
                        level={level!}
                        name={evalProps?.name}
                        font={evalProps?.fontName ?? 'Roboto'}
                        fontSize={evalProps?.fontSize ?? 12}
                    >
                        <div className=" flex flex-col items-center gap-1 h-fit p-0">
                            <span className="rounded-lg p-2 bg-dinoBotBlue text-dinoBotWhite text-wrap">
                                <Eye />
                            </span>
                            {t('preview')} <br /> {t('prv_eval')}
                        </div>
                    </DialogPreviewEval>
                </div>
            </div>
            <ScrollArea className="">
                {isLoading ? (
                    <ExoContentLoading />
                ) : (
                    exos.map((exo, i) => (
                        <ExoContent key={i} exoNum={i} exo={exo} />
                    ))
                )}
                <div className="flex justify-center mt-4 w-full">
                    <Button
                        variant="outline"
                        className={`text-dinoBotBlue hover:text-dinoBotVibrantBlue border-dinoBotBlue px-2 ${exos.at(-1)?.questions?.at(-1)?.content ? '' : 'hidden'}`}
                        onClick={() => addExo({ questions: [{}] })}
                        disabled={!havePossibilityToAdd()}
                    >
                        <Plus />
                        {t('add_exo')}
                    </Button>
                </div>
            </ScrollArea>
            <div className="mt-6 flex justify-evenly px-[18%]">
                {/* <Button variant='outline' className='border-dinoBotGray' onClick={()=>save('DRAFT',1)}disabled={isEmptyOrNull(exos.at(0)?.questions?.at(0)?.content)||(isAdd&&loadId===1)}>
                    {(isAdd&&loadId===1) ? <>
                        <Loader2 className="mr-2 size-4 animate-spin" />
                        {t('submit.loading')}
                    </>:
                    isUpdate?t('submit.update_br'):t('submit.save_br')}</Button> */}
                <Button
                    variant="default"
                    className="bg-dinoBotBlue hover:bg-dinoBotVibrantBlue text-dinoBotWhite"
                    onClick={() => save('IN_PROGRESS', 2)}
                    disabled={
                        isEmptyOrNull(exos.at(0)?.questions?.at(0)?.content) ||
                        (isAdd && loadId === 2)
                    }
                >
                    {isAdd && loadId === 2 ? (
                        <>
                            <Loader2 className="mr-2 size-4 animate-spin" />
                            {t('submit.loading')}
                        </>
                    ) : isUpdate ? (
                        t('submit.update')
                    ) : (
                        t('submit.create')
                    )}
                </Button>
                {evalProps?.status !== 'ASSIGNED' && (
                    <Button
                        variant="default"
                        className="bg-dinoBotBlue hover:bg-dinoBotVibrantBlue text-dinoBotWhite"
                        onClick={scheduleEvaluation}
                        disabled={
                            isEmptyOrNull(
                                exos.at(0)?.questions?.at(0)?.content
                            ) ||
                            (isAdd && loadId === 3)
                        }
                    >
                        {isAdd && loadId === 3 ? (
                            <>
                                <Loader2 className="mr-2 size-4 animate-spin" />
                                {t('submit.loading')}
                            </>
                        ) : (
                            t('submit.shedule')
                        )}
                    </Button>
                )}
            </div>
        </div>
    )
}

export default CreateEvaluationExo

/*
  Warnings:

  - Added the required column `tm_part_id` to the `praxeo_tasks` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "praxeo_tasks" ADD COLUMN     "tm_part_id" TEXT NOT NULL;

-- AddForeignKey
ALTER TABLE "praxeo_tasks" ADD CONSTRAINT "praxeo_tasks_tm_part_id_fkey" FOREIGN KEY ("tm_part_id") REFERENCES "tm_parts"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

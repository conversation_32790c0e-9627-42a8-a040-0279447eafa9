import {
    createEvent,
    deleteEvent,
    updateEvent
} from '@/lib/control-mode/services/event/actions'
import { Class } from '@/lib/control-mode/types/class/types'
import { EventList, EventRow } from '@/lib/control-mode/types/event/types'
import { classEmit } from '@/lib/sse/class/class-events'
import { createSelectors } from '@/lib/stores/selectors.store'
import { Home, Timer } from 'lucide-react'
import { createElement, ReactElement } from 'react'
import { toast } from 'sonner'
import { create } from 'zustand'
type controleStoreType = controleStateType & controleActionType
export type eventCalendarType = {
    id: string
    title: string
    start: Date
    end: Date
    color: string
    type: filterType
    open: boolean
    icone: ReactElement
}
export type filterType = 'TOUS' | 'HOMEWORK' | 'CONTROL' | 'SORTIE'
type controleStateType = {
    events: EventList
    classes: Class[]
    eventsCalendar: eventCalendarType[]
    open: boolean
    filter: filterType
}

type controleActionType = {
    setEvents: (events: EventList) => void
    setClasses: (classes: Class[]) => void
    addEvent: (event: EventRow, classids: string[]) => void
    updateEvent: (event: EventRow, classids: string[]) => void
    deleteEvent: (event: EventRow) => void
    setOpen: (open: boolean, isupdate: boolean, id?: string) => void
    setFilter: (filter: filterType) => void
}

const inistialCalendarState: controleStateType = {
    events: [],
    classes: [],
    eventsCalendar: [],
    open: false,
    filter: 'TOUS'
}
export const useCalendarStore = create<controleStoreType>(set => ({
    ...inistialCalendarState,
    setEvents(events) {
        set(state => ({
            ...state,
            events,
            eventsCalendar: events.map(e => ({
                id: e.id,
                title: e.title,
                start: e.date,
                end: e.date,
                type: e.type,
                open: false,
                color: e.type === 'CONTROL' ? '#FF5E0E' : '#2AAFFB',
                icone: createElement(e.type === 'HOMEWORK' ? Home : Timer)
            }))
        }))
    },
    setClasses(classes) {
        set(state => ({ ...state, classes: classes }))
    },
    async addEvent(event, classids) {
        try {
            event = await createEvent(event, classids)
            const eventCalendar: eventCalendarType = {
                id: event.id,
                title: event.title,
                start: event.date,
                open: false,
                end: event.date,
                type: event.type,
                color: event.type === 'CONTROL' ? '#FF5E0E' : '#2AAFFB',
                icone: createElement(event.type === 'HOMEWORK' ? Home : Timer)
            }
            set(state => ({
                ...state,
                events: [...state.events, event],
                eventsCalendar: [...state.eventsCalendar, eventCalendar]
            }))
            await classEmit({
                classIds: classids,
                event: 'class:event-created',
                data: event.id
            })
            toast.success("L'événement a été ajouté avec succès")
        } catch (error) {
            console.error(error)
            toast.error(
                "Une erreur s'est produite lors de l'ajout de l'événement"
            )
        }
    },
    async updateEvent(event, classids) {
        try {
            event = await updateEvent(event.id, event, classids)
            const eventCalendar: eventCalendarType = {
                id: event.id,
                title: event.title,
                start: event.date,
                end: event.date,
                open: false,
                type: event.type,
                color: event.type === 'CONTROL' ? '#FF5E0E' : '#2AAFFB',
                icone: createElement(event.type === 'HOMEWORK' ? Home : Timer)
            }
            set(state => ({
                ...state,
                events: state.events.map(e => (e.id === event.id ? event : e)),
                eventsCalendar: state.eventsCalendar.map(e =>
                    e.id === event.id ? eventCalendar : e
                )
            }))
            await classEmit({
                classIds: classids,
                event: 'class:event-updated',
                data: event.id
            })
            toast.success("L'événement a été mis à jour avec succès")
        } catch (error) {
            console.error(error)
            toast.error(
                "Une erreur s'est produite lors de la mise à jour de l'événement"
            )
        }
    },
    async deleteEvent(event) {
        try {
            await deleteEvent(event.id)
            set(state => ({
                ...state,
                events: state.events.filter(e => e.id !== event.id),
                eventsCalendar: state.eventsCalendar.filter(
                    e => e.id !== event.id
                )
            }))
            await classEmit({
                classIds: event.assignedClasses.map(c => c.classId),
                event: 'class:event-deleted',
                data: event.id
            })
            toast.success("L'événement a été supprimé avec succès")
        } catch (error) {
            console.error(error)
            toast.error(
                "Une erreur s'est produite lors de la suppression de l'événement"
            )
        }
    },
    setFilter(filter) {
        set(state => ({ ...state, filter }))
    },
    setOpen(open, isupdate, id) {
        if (isupdate)
            set(state => ({
                ...state,
                eventsCalendar: state.eventsCalendar.map(e =>
                    e.id === id ? { ...e, open } : e
                )
            }))
        else set(state => ({ ...state, open: open }))
    }
}))
export const selectUseCalendarStore = createSelectors(useCalendarStore)

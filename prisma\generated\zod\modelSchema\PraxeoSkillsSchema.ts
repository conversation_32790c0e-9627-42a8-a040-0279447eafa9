import { z } from 'zod';
import { JsonValueSchema } from '../inputTypeSchemas/JsonValueSchema'
import { LevelDomainWithRelationsSchema, LevelDomainPartialWithRelationsSchema, LevelDomainOptionalDefaultsWithRelationsSchema } from './LevelDomainSchema'
import type { LevelDomainWithRelations, LevelDomainPartialWithRelations, LevelDomainOptionalDefaultsWithRelations } from './LevelDomainSchema'

/////////////////////////////////////////
// PRAXEO SKILLS SCHEMA
/////////////////////////////////////////

export const PraxeoSkillsSchema = z.object({
  id: z.number(),
  contents: JsonValueSchema,
  levelDomainId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type PraxeoSkills = z.infer<typeof PraxeoSkillsSchema>

/////////////////////////////////////////
// PRAXEO SKILLS PARTIAL SCHEMA
/////////////////////////////////////////

export const PraxeoSkillsPartialSchema = PraxeoSkillsSchema.partial()

export type PraxeoSkillsPartial = z.infer<typeof PraxeoSkillsPartialSchema>

/////////////////////////////////////////
// PRAXEO SKILLS OPTIONAL DEFAULTS SCHEMA
/////////////////////////////////////////

export const PraxeoSkillsOptionalDefaultsSchema = PraxeoSkillsSchema.merge(z.object({
  id: z.number().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type PraxeoSkillsOptionalDefaults = z.infer<typeof PraxeoSkillsOptionalDefaultsSchema>

/////////////////////////////////////////
// PRAXEO SKILLS RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoSkillsRelations = {
  levelDomain: LevelDomainWithRelations;
};

export type PraxeoSkillsWithRelations = z.infer<typeof PraxeoSkillsSchema> & PraxeoSkillsRelations

export const PraxeoSkillsWithRelationsSchema: z.ZodType<PraxeoSkillsWithRelations> = PraxeoSkillsSchema.merge(z.object({
  levelDomain: z.lazy(() => LevelDomainWithRelationsSchema),
}))

/////////////////////////////////////////
// PRAXEO SKILLS OPTIONAL DEFAULTS RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoSkillsOptionalDefaultsRelations = {
  levelDomain: LevelDomainOptionalDefaultsWithRelations;
};

export type PraxeoSkillsOptionalDefaultsWithRelations = z.infer<typeof PraxeoSkillsOptionalDefaultsSchema> & PraxeoSkillsOptionalDefaultsRelations

export const PraxeoSkillsOptionalDefaultsWithRelationsSchema: z.ZodType<PraxeoSkillsOptionalDefaultsWithRelations> = PraxeoSkillsOptionalDefaultsSchema.merge(z.object({
  levelDomain: z.lazy(() => LevelDomainOptionalDefaultsWithRelationsSchema),
}))

/////////////////////////////////////////
// PRAXEO SKILLS PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type PraxeoSkillsPartialRelations = {
  levelDomain?: LevelDomainPartialWithRelations;
};

export type PraxeoSkillsPartialWithRelations = z.infer<typeof PraxeoSkillsPartialSchema> & PraxeoSkillsPartialRelations

export const PraxeoSkillsPartialWithRelationsSchema: z.ZodType<PraxeoSkillsPartialWithRelations> = PraxeoSkillsPartialSchema.merge(z.object({
  levelDomain: z.lazy(() => LevelDomainPartialWithRelationsSchema),
})).partial()

export type PraxeoSkillsOptionalDefaultsWithPartialRelations = z.infer<typeof PraxeoSkillsOptionalDefaultsSchema> & PraxeoSkillsPartialRelations

export const PraxeoSkillsOptionalDefaultsWithPartialRelationsSchema: z.ZodType<PraxeoSkillsOptionalDefaultsWithPartialRelations> = PraxeoSkillsOptionalDefaultsSchema.merge(z.object({
  levelDomain: z.lazy(() => LevelDomainPartialWithRelationsSchema),
}).partial())

export type PraxeoSkillsWithPartialRelations = z.infer<typeof PraxeoSkillsSchema> & PraxeoSkillsPartialRelations

export const PraxeoSkillsWithPartialRelationsSchema: z.ZodType<PraxeoSkillsWithPartialRelations> = PraxeoSkillsSchema.merge(z.object({
  levelDomain: z.lazy(() => LevelDomainPartialWithRelationsSchema),
}).partial())

export default PraxeoSkillsSchema;

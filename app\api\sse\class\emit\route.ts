

import { ClassSSEEvents } from "@/lib/sse/class-events";


export type JsonPayload<T> = {
    classIds: string[];
    event: ClassSSEEvents;
    message?: string;
    data?: T;
};

type SSEClients = Record<string, Record<string, ReadableStreamDefaultController[]>>;

if (!globalThis.sseClients) {
    globalThis.sseClients = {};
}

declare global {
    var sseClients: SSEClients;
}

export async function POST(req: Request): Promise<Response> {
    const payload = await req.json() as JsonPayload<any>;
    const { classIds, event, message, data } = payload;

    // Determine which event listener this should go to
    const eventListener = event.includes("evaluation") ? "evaluations" : "events";


    const encoder = new TextEncoder();

    // Create the SSE message with STRICT formatting
    // EventSource requires: event: eventName\ndata: jsonData\n\n
    const messageData = JSON.stringify({
        classIds,
        event,
        message,
        data
    });

    const sseMessage = encoder.encode(
        `event: ${eventListener}\ndata: ${messageData}\n\n`
    );

    if (classIds.length > 0) {
        classIds.forEach(classId => {
            const eventClients = globalThis.sseClients[classId]?.[eventListener] || [];

            eventClients.forEach((controller, index) => {
                try {
                    controller.enqueue(sseMessage);
                } catch (err) {
                    const clients = globalThis.sseClients[classId][eventListener];
                    if (clients) {
                        const controllerIndex = clients.indexOf(controller);
                        if (controllerIndex > -1) {
                            clients.splice(controllerIndex, 1);
                        }
                    }
                }
            });
        });
    }


    return new Response(JSON.stringify({
        success: true,
        eventType: eventListener,
        rawMessage: new TextDecoder().decode(sseMessage)
    }), {
        headers: { "Content-Type": "application/json" },
    });
}
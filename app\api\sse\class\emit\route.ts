import { ClassSSEPayload } from "@/lib/sse/class/schema";
import { logger } from "@/logger/logger";

export async function POST(req: Request): Promise<Response> {
    try {
        const body = await req.json();
        const parsed = ClassSSEPayload.parse(body);

        const { classIds, event, message, data } = parsed;
        const eventListener = event.includes("evaluation") ? "evaluations" : "events";

        const encoder = new TextEncoder();
        const messageData = JSON.stringify(parsed);

        const sseMessage = encoder.encode(
            `event: ${eventListener}\ndata: ${messageData}\n\n`
        );

        classIds.forEach(classId => {
            const eventClients = globalThis.sseClients[classId]?.[eventListener] || [];
            eventClients.forEach((controller, index) => {
                try {
                    controller.enqueue(sseMessage);
                } catch {
                    const clients = globalThis.sseClients[classId][eventListener];
                    if (clients) {
                        clients.splice(clients.indexOf(controller), 1);
                    }
                }
            });
        });

        return new Response(JSON.stringify({ success: true }), {
            headers: { "Content-Type": "application/json" },
        });

    } catch (err) {
        logger.error(`Validation failed: ${err}`);
        return new Response(JSON.stringify({
            success: false,
            error: err instanceof Error ? err.message : "Invalid payload"
        }), {
            status: 400,
            headers: { "Content-Type": "application/json" },
        });
    }
}

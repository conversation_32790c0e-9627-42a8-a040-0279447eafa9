model PraxeoSkills {
  id       Int  @id @default(autoincrement())
  contents Json

  levelDomainId String      @map("level_domain_id")
  levelDomain   LevelDomain @relation(fields: [levelDomainId], references: [id])

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  @@map("praxeo_skills")
}

model PraxeoStructs {
  id       Int  @id @default(autoincrement())
  contents Json

  partId String @map("part_id")
  part   Part   @relation(fields: [partId], references: [id])

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  @@map("praxeo_structs")
}

model PraxeoTask {
  id Int @id @default(autoincrement())

  description String
  examples Json?
  isDisabled Boolean @default(false)
  
  praxeoTaskSkills PraxeoTaskSkill[]

  tmPartId String   @map("tm_part_id")
  tmPart   Part @relation(fields: [tmPartId], references: [id])

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  @@map("praxeo_tasks")
}

model PraxeoTaskSkill {
  id Int @id @default(autoincrement())

  praxeoTaskId Int        @map("praxeo_task_id")
  praxeoTask   PraxeoTask @relation(fields: [praxeoTaskId], references: [id])

  tmSkillId String   @map("tm_skill_id")
  tmSkill   TmSkills @relation(fields: [tmSkillId], references: [id])

  @@map("praxeo_task_skills")
}

model TmSkills {
  id       String @id @default(dbgenerated("gen_random_uuid()"))
  description String?

  partId String @map("part_id")
  part   Part   @relation(fields: [partId], references: [id])

  praxeoTaskSkills PraxeoTaskSkill[]

  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime? @updatedAt @map("updated_at")

  @@map("tm_skills")
}
import {
    AccordionContent,
    AccordionItem,
    AccordionTrigger
} from '@/components/ui/accordion'
import { getPlannedControlsByThemeId } from '@/lib/control-mode/actions'
import { Theme } from '@/prisma/generated/zod/modelSchema/ThemeSchema'
import React, { useEffect, useState } from 'react'
import EvaluationCard from './evaluation-card'
import {
    ControlPartial,
    ControlPartialWithRelations
} from '@/prisma/generated/zod/modelSchema/ControlSchema'
import { useTranslations } from 'next-intl'
import { PlannedEvaluationWithPartialRelations } from '@/prisma/generated/zod/modelSchema/PlannedEvaluationSchema'
import { ClassSSE } from '@/lib/sse/class-events'

type BlockProps = {
    theme: Theme
    triggerHighlight: () => void
}

const ThemeBlock = ({ theme, triggerHighlight }: BlockProps) => {
    const [controls, setControls] = useState<
        ControlPartialWithRelations &
            {
                plannedId: string
                availableDate: Date | null
                controlId: string
            }[]
    >()
    const translate = useTranslations('app.courses.index')

    async function fetchControls() {
        const response = (await getPlannedControlsByThemeId(
            theme.id
        )) as PlannedEvaluationWithPartialRelations[]
        setControls(
            response.map(item => {
                return {
                    ...item.control,
                    plannedId: item.id,
                    availableDate: item?.availableDate,
                    controlId: item.controlId
                }
            })
        )
    }

    useEffect(() => {
        let source: EventSource
        ;(async () => {
            await fetchControls()
            if (!theme.classId) return
            source = ClassSSE.listen<string>(
                theme.classId,
                'evaluations',
                async payload => {
                    console.log('🚀 ~ ; ~ payload:', payload)
                    if (payload.data === theme.id) {
                        await fetchControls()
                        triggerHighlight()
                    }
                }
            )
        })()

        return () => {
            source ? source.close() : undefined
        }
    }, [])

    return (
        <AccordionItem
            value={theme.id}
            className="border-2 rounded-xl border-dinoBotGray/50 overflow-hidden"
        >
            <AccordionTrigger className="rounded-lg px-4 bg-dinoBotLightGray/50 text-dinoBotDarkGray">
                {theme.name}
            </AccordionTrigger>
            <AccordionContent className="px-4 py-2 flex gap-2 flex-wrap w-fit">
                {controls?.map((control, i) => (
                    <EvaluationCard
                        key={i}
                        evaluation={control as ControlPartial}
                        plannedId={control.plannedId}
                        availableDate={control.availableDate!}
                    />
                ))}
                {!controls?.length && (
                    <div className="text-dinoBotGray">
                        {translate('accordion.not-found')}
                    </div>
                )}
            </AccordionContent>
        </AccordionItem>
    )
}

export default ThemeBlock

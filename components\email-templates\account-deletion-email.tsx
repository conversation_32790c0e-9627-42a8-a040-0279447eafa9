import {
    <PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>n,
    Con<PERSON>er,
    <PERSON>,
    <PERSON><PERSON>,
    Html,
    Img,
    Link,
    Preview,
    Row,
    Section,
    Tailwind,
    Text
} from '@react-email/components'
import { addDays, format } from 'date-fns'
import { enUS, fr } from 'date-fns/locale'
import { getLocale, getTranslations } from 'next-intl/server'
import * as React from 'react'
import { getLangDir } from 'rtl-detect'

interface AccountDeletionEmailProps {
    email?: string
    cancellationToken?: string
}

export default async function AccountDeletionEmail({
    email = '<EMAIL>',
    cancellationToken = '123456'
}: AccountDeletionEmailProps) {
    const t = await getTranslations('lib.email.accountDeletion')
    const commonT = await getTranslations('email')

    const locale = await getLocale()
    const direction = getLangDir(locale)

    // Calculate deletion date (30 days from now)
    const currentDate = new Date()
    const deletionDate = addDays(currentDate, 30)

    // Format the date and time based on locale
    const dateLocale = locale === 'fr' ? fr : enUS
    const formattedDate = format(deletionDate, 'PPP', { locale: dateLocale })
    const formattedTime = format(deletionDate, 'p', { locale: dateLocale })

    // URLs from environment variables
    const baseUrl = process.env.BASE_URL || 'http://localhost:3000'
    const legalNoticeUrl =
        process.env.EMAIL_LEGAL_NOTICE_URL ||
        'https://dinobot.fr/mentions-legales'
    const dataProtectionUrl =
        process.env.EMAIL_DATA_PROTECTION_URL ||
        'https://dinobot.fr/protection-donnees'
    const instagramUrl =
        process.env.EMAIL_INSTAGRAM_URL || 'https://instagram.com/dinobot'
    const linkedinUrl =
        process.env.EMAIL_LINKEDIN_URL || 'https://linkedin.com/company/dinobot'
    const facebookUrl =
        process.env.EMAIL_FACEBOOK_URL || 'https://facebook.com/dinobot'
    const youtubeUrl =
        process.env.EMAIL_YOUTUBE_URL || 'https://youtube.com/dinobot'

    return (
        <Html dir={direction}>
            <Head />
            <Preview>{t('preview')}</Preview>
            <Tailwind>
                <Body className="bg-white font-sans">
                    <Container className="mx-auto p-0 max-w-[600px]">
                        {/* Header */}
                        <Section className="bg-blue-600 p-5 text-center text-white">
                            <Heading className="text-3xl font-bold my-2.5 text-white">
                                {t('greeting')}
                            </Heading>
                        </Section>

                        {/* Main content */}
                        <Section className="p-5">
                            <Text className="text-base leading-6 text-gray-800 my-5">
                                {t('confirmationInfo')}
                            </Text>
                            <Text className="text-base leading-6 text-gray-800 my-5">
                                {t('deletionScheduled', {
                                    date: formattedDate,
                                    time: formattedTime
                                })}
                            </Text>
                            <Text className="text-base leading-6 text-gray-800 my-5">
                                {t('cancellationInfo')}
                            </Text>

                            {/* CTA Button */}
                            <Section className="text-center my-7">
                                <Button
                                    className="bg-yellow-400 rounded-full text-black text-base font-bold no-underline inline-block px-5 py-3"
                                    href={`${baseUrl}/${locale}/account-deletion-requested`}
                                >
                                    {t('button')}
                                </Button>
                            </Section>

                            <Text className="text-base leading-6 text-gray-800 my-5">
                                {t('ignoreInfo')}
                            </Text>
                            <Text className="text-base leading-6 text-gray-800 my-5">
                                {t('thanks')}
                                <br />
                                {t('signature')}
                            </Text>
                            {/* Logo */}
                            <Section className="text-center my-7 w-full flex item-center justify-center">
                                <Img
                                    src={`${baseUrl}/dinobot-logo-small.svg`}
                                    width="60"
                                    height="60"
                                    alt="Logo Dinobot"
                                />
                            </Section>
                        </Section>

                        {/* Social media footer */}
                        <Section className="bg-blue-600 p-5 text-center text-white">
                            <Text className="text-base font-bold mb-4 text-white">
                                {commonT('common.followUs')}
                            </Text>
                            <Row className="w-60 mx-auto">
                                <Column className="w-15 text-center">
                                    <Link href={instagramUrl}>
                                        <Img
                                            src={`${baseUrl}/instagram.png`}
                                            width="32"
                                            height="32"
                                            alt="Instagram"
                                        />
                                    </Link>
                                </Column>
                                <Column className="w-15 text-center">
                                    <Link href={linkedinUrl}>
                                        <Img
                                            src={`${baseUrl}/linkedin.png`}
                                            width="32"
                                            height="32"
                                            alt="LinkedIn"
                                        />
                                    </Link>
                                </Column>
                                <Column className="w-15 text-center">
                                    <Link href={facebookUrl}>
                                        <Img
                                            src={`${baseUrl}/facebook.png`}
                                            width="32"
                                            height="32"
                                            alt="Facebook"
                                        />
                                    </Link>
                                </Column>
                                <Column className="w-15 text-center">
                                    <Link href={youtubeUrl}>
                                        <Img
                                            src={`${baseUrl}/youtube.png`}
                                            width="32"
                                            height="32"
                                            alt="YouTube"
                                        />
                                    </Link>
                                </Column>
                            </Row>
                        </Section>

                        {/* Legal footer */}
                        <Section className="p-5 text-center bg-gray-100">
                            <Text className="text-base font-bold m-0 text-gray-800">
                                {commonT('common.company')}
                            </Text>
                            <Text className="text-sm my-1 mb-5 text-gray-600">
                                {commonT('common.address')}
                            </Text>

                            <Section className="my-2.5">
                                <Link
                                    href={legalNoticeUrl}
                                    className="text-blue-600 no-underline"
                                >
                                    {commonT('common.legalNotice')}
                                </Link>{' '}
                                |{' '}
                                <Link
                                    href={dataProtectionUrl}
                                    className="text-blue-600 no-underline"
                                >
                                    {commonT('common.dataProtection')}
                                </Link>
                            </Section>

                            <Text className="text-xs text-gray-500 my-5 mt-1">
                                {commonT('common.automaticEmail')}
                            </Text>

                            <Text className="text-xs text-gray-500 my-1">
                                {commonT('common.unsubscribeText')}{' '}
                                <Link
                                    href={`${baseUrl}/`}
                                    className="text-gray-600 underline"
                                >
                                    {commonT('common.unsubscribeLink')}
                                </Link>
                            </Text>
                        </Section>
                    </Container>
                </Body>
            </Tailwind>
        </Html>
    )
}

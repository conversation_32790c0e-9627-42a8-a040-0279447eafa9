@import url('https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css');
@import url('https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.snow.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 240 10% 3.9%;

        --card: 0 0% 100%;
        --card-foreground: 240 10% 3.9%;

        --popover: 0 0% 100%;
        --popover-foreground: 240 10% 3.9%;

        --primary: 240 5.9% 10%;
        --primary-foreground: 0 0% 98%;

        --secondary: 240 4.8% 95.9%;
        --secondary-foreground: 240 5.9% 10%;

        --muted: 240 4.8% 95.9%;
        --muted-foreground: 240 3.8% 46.1%;

        --accent: 240 4.8% 95.9%;
        --accent-foreground: 240 5.9% 10%;

        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;

        --border: 240 5.9% 90%;
        --input: 240 5.9% 90%;
        --ring: 240 10% 3.9%;

        --radius: 0.5rem;

        --page-highlight: 211.18 76% 39.22%;
    }

    .dark {
        --background: 240 10% 3.9%;
        --foreground: 0 0% 98%;

        --card: 240 10% 3.9%;
        --card-foreground: 0 0% 98%;

        --popover: 240 10% 3.9%;
        --popover-foreground: 0 0% 98%;

        --primary: 0 0% 98%;
        --primary-foreground: 240 5.9% 10%;

        --secondary: 240 3.7% 15.9%;
        --secondary-foreground: 0 0% 98%;

        --muted: 240 3.7% 15.9%;
        --muted-foreground: 240 5% 64.9%;

        --accent: 240 3.7% 15.9%;
        --accent-foreground: 0 0% 98%;

        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;

        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;
        --ring: 240 4.9% 83.9%;

        --page-highlight: 211.18 76% 39.22%;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}
*,
::after,
::before {
    /* box-shadow: 0 0 0 3px  lightsalmon inset; */
}

.overflow-overlay {
    overflow: overlay;
}

@keyframes loading {
    0% {
        width: 0;
    }
    100% {
        width: 90%;
    }
}

.loading-bar {
    animation: loading 1s linear;
}

.custom-scroller::-webkit-scrollbar {
    @apply w-[6px];
}

/* Track */
.custom-scroller::-webkit-scrollbar-track {
    @apply bg-dinoBotLightGray;
}

/* Handle */
.custom-scroller::-webkit-scrollbar-thumb {
    @apply bg-dinoBotGray rounded-full;
}

/* Handle on hover */
.custom-scroller::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.app-scroller::-webkit-scrollbar {
    @apply w-2;
}

/* Track */
.app-scroller::-webkit-scrollbar-track {
    @apply bg-dinoBotBlue/10;
}

/* Handle */
.app-scroller::-webkit-scrollbar-thumb {
    @apply bg-dinoBotBlue/60 rounded-full;
}

/* Handle on hover */
.app-scroller::-webkit-scrollbar-thumb:hover {
    @apply bg-dinoBotBlue;
}

.chat .ql-toolbar {
    display: none;
}

.rtl .ql-editor {
    direction: rtl;
    text-align: right;
}

.chat .ql-container {
    width: 100%;
    height: 100%;
    border: none !important;
}

/* Mathjax inline */
mjx-container svg {
    display: inline !important;
}

.no-arrows::-webkit-inner-spin-button,
.no-arrows::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.no-arrows {
    -moz-appearance: textfield;
    appearance: none;
}
.animate-spin-reverse {
    animation: spin-reverse 1s linear infinite;
}

@keyframes spin-reverse {
    from {
        transform: rotate(360deg);
    }
    to {
        transform: rotate(0deg);
    }
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground;
    }
}


enum FeatureFlagName {
  STUDENT_CLASSES_VIEW
  STUDENT_CHAT_MODE
  STUDENT_EXERCISE_MODE
  STUDENT_EVALUATION_MODE
  STUDENT_EXAM_MODE
}

model FeatureFlag {
  id String @id @default(dbgenerated("gen_random_uuid()"))

  featureName FeatureFlagName @unique @map("feature_name")

  isEnabled Boolean @default(true) @map("is_enabled")

  description String? @map("description")

  rang Int? @unique

  updatedById String? @map("updated_by_id")
  updatedBy   User?   @relation(fields: [updatedById], references: [id], onDelete: SetNull)

  createdAt DateTime @default(now()) @map("created_at")

  updatedAt DateTime @updatedAt @map("updated_at")

  @@unique([id, updatedById])
  @@map("feature_flags")
}

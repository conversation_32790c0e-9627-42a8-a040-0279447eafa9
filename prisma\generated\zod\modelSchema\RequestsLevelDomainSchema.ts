import { z } from 'zod';
import { RequestStatusSchema } from '../inputTypeSchemas/RequestStatusSchema'
import { UserWithRelationsSchema, UserPartialWithRelationsSchema, UserOptionalDefaultsWithRelationsSchema } from './UserSchema'
import type { UserWithRelations, UserPartialWithRelations, UserOptionalDefaultsWithRelations } from './UserSchema'
import { RequestLevelDomainItemWithRelationsSchema, RequestLevelDomainItemPartialWithRelationsSchema, RequestLevelDomainItemOptionalDefaultsWithRelationsSchema } from './RequestLevelDomainItemSchema'
import type { RequestLevelDomainItemWithRelations, RequestLevelDomainItemPartialWithRelations, RequestLevelDomainItemOptionalDefaultsWithRelations } from './RequestLevelDomainItemSchema'

/////////////////////////////////////////
// REQUESTS LEVEL DOMAIN SCHEMA
/////////////////////////////////////////

export const RequestsLevelDomainSchema = z.object({
  status: RequestStatusSchema,
  id: z.string(),
  userId: z.string(),
  explanation: z.string(),
  comment: z.string().nullable(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type RequestsLevelDomain = z.infer<typeof RequestsLevelDomainSchema>

/////////////////////////////////////////
// REQUESTS LEVEL DOMAIN PARTIAL SCHEMA
/////////////////////////////////////////

export const RequestsLevelDomainPartialSchema = RequestsLevelDomainSchema.partial()

export type RequestsLevelDomainPartial = z.infer<typeof RequestsLevelDomainPartialSchema>

/////////////////////////////////////////
// REQUESTS LEVEL DOMAIN OPTIONAL DEFAULTS SCHEMA
/////////////////////////////////////////

export const RequestsLevelDomainOptionalDefaultsSchema = RequestsLevelDomainSchema.merge(z.object({
  status: RequestStatusSchema.optional(),
  id: z.string().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type RequestsLevelDomainOptionalDefaults = z.infer<typeof RequestsLevelDomainOptionalDefaultsSchema>

/////////////////////////////////////////
// REQUESTS LEVEL DOMAIN RELATION SCHEMA
/////////////////////////////////////////

export type RequestsLevelDomainRelations = {
  user: UserWithRelations;
  requestLevelDomains: RequestLevelDomainItemWithRelations[];
};

export type RequestsLevelDomainWithRelations = z.infer<typeof RequestsLevelDomainSchema> & RequestsLevelDomainRelations

export const RequestsLevelDomainWithRelationsSchema: z.ZodType<RequestsLevelDomainWithRelations> = RequestsLevelDomainSchema.merge(z.object({
  user: z.lazy(() => UserWithRelationsSchema),
  requestLevelDomains: z.lazy(() => RequestLevelDomainItemWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// REQUESTS LEVEL DOMAIN OPTIONAL DEFAULTS RELATION SCHEMA
/////////////////////////////////////////

export type RequestsLevelDomainOptionalDefaultsRelations = {
  user: UserOptionalDefaultsWithRelations;
  requestLevelDomains: RequestLevelDomainItemOptionalDefaultsWithRelations[];
};

export type RequestsLevelDomainOptionalDefaultsWithRelations = z.infer<typeof RequestsLevelDomainOptionalDefaultsSchema> & RequestsLevelDomainOptionalDefaultsRelations

export const RequestsLevelDomainOptionalDefaultsWithRelationsSchema: z.ZodType<RequestsLevelDomainOptionalDefaultsWithRelations> = RequestsLevelDomainOptionalDefaultsSchema.merge(z.object({
  user: z.lazy(() => UserOptionalDefaultsWithRelationsSchema),
  requestLevelDomains: z.lazy(() => RequestLevelDomainItemOptionalDefaultsWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// REQUESTS LEVEL DOMAIN PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type RequestsLevelDomainPartialRelations = {
  user?: UserPartialWithRelations;
  requestLevelDomains?: RequestLevelDomainItemPartialWithRelations[];
};

export type RequestsLevelDomainPartialWithRelations = z.infer<typeof RequestsLevelDomainPartialSchema> & RequestsLevelDomainPartialRelations

export const RequestsLevelDomainPartialWithRelationsSchema: z.ZodType<RequestsLevelDomainPartialWithRelations> = RequestsLevelDomainPartialSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  requestLevelDomains: z.lazy(() => RequestLevelDomainItemPartialWithRelationsSchema).array(),
})).partial()

export type RequestsLevelDomainOptionalDefaultsWithPartialRelations = z.infer<typeof RequestsLevelDomainOptionalDefaultsSchema> & RequestsLevelDomainPartialRelations

export const RequestsLevelDomainOptionalDefaultsWithPartialRelationsSchema: z.ZodType<RequestsLevelDomainOptionalDefaultsWithPartialRelations> = RequestsLevelDomainOptionalDefaultsSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  requestLevelDomains: z.lazy(() => RequestLevelDomainItemPartialWithRelationsSchema).array(),
}).partial())

export type RequestsLevelDomainWithPartialRelations = z.infer<typeof RequestsLevelDomainSchema> & RequestsLevelDomainPartialRelations

export const RequestsLevelDomainWithPartialRelationsSchema: z.ZodType<RequestsLevelDomainWithPartialRelations> = RequestsLevelDomainSchema.merge(z.object({
  user: z.lazy(() => UserPartialWithRelationsSchema),
  requestLevelDomains: z.lazy(() => RequestLevelDomainItemPartialWithRelationsSchema).array(),
}).partial())

export default RequestsLevelDomainSchema;

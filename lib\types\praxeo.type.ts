import { z } from 'zod'

const TheorieSchema = z.object({
    id: z.string(),
    description: z.string(),
    technologies_associees: z.array(z.string())
})

const TechniqueSchema = z.object({
    id: z.string(),
    description: z.string(),
    type_tache_id: z.string()
})

const TechnologieSchema = z.object({
    id: z.string(),
    description: z.string(),
    techniques_associees: z.array(z.string())
})

const ExempleSchema = z.object({
    enonce: z.string(),
    niveau_difficulte: z.string().optional(),
    solution: z.string()
})

const TypeDeTacheSchema = z.object({
    id: z.string(),
    competencies: z.array(z.string()),
    examples: z.array(ExempleSchema),
    description: z.string()
})

const PraxeologieSchema = z.object({
    theories: z.array(TheorieSchema),
    techniques: z.array(TechniqueSchema),
    technologies: z.array(TechnologieSchema),
    types_de_taches: z.array(TypeDeTacheSchema)
})

const PartieSchema = z.object({
    id: z.string(),
    titre: z.string(),
    praxeologie: PraxeologieSchema
})

const ChapitreSchema = z.object({
    id: z.string(),
    titre: z.string(),
    parties: z.array(PartieSchema)
})

const MetadataSchema = z.object({
    niveau: z.string(),
    matiere: z.string(),
    generation_date: z.string()
})

export const PraxeoSchema = z.object({
    chapitre: ChapitreSchema,
    metadata: MetadataSchema,
    competences_disponibles: z.record(z.unknown())
})

//parent type
export type PraxeoType = z.infer<typeof PraxeoSchema>
//other types
export type TheorieType = z.infer<typeof TheorieSchema>
export type TechniqueType = z.infer<typeof TechniqueSchema>
export type TechnologieType = z.infer<typeof TechnologieSchema>
export type TypeDeTacheType = z.infer<typeof TypeDeTacheSchema>
export type PraxeologieType = z.infer<typeof PraxeologieSchema>
export type PartieType = z.infer<typeof PartieSchema>
export type ChapitreType = z.infer<typeof ChapitreSchema>
export type MetadataType = z.infer<typeof MetadataSchema>

export type Competency = {
    description: string
}

export type TaskWithCompetenciesAndExamples = {
    id: number
    description: string
    examples: {
        enonce: string
        niveau_difficulte: string
        solution: string
    }[]
    competencies: Competency[]
    // Add other fields from PraxeoTask if needed
}

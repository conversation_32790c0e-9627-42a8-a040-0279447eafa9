{"completeInscription": {"student": {"1": {"preview": "Tu n'as plus qu'une étape pour accéder à Dinobot ! 🎓", "subject": "Tu n'as plus qu'une étape pour accéder à Dinobot ! 🎓", "greeting": "Salut ,", "intro": "Tu as commencé ton inscription sur <PERSON><PERSON> hier, mais il te reste juste une dernière étape à finaliser.", "buttonText": "Finaliser mon inscription →", "mainTitle": "Rappel : a<PERSON><PERSON>, tu auras accès à :", "features": ["Un chatbot personnalisé pour t'aider dans tes matières", "Des exercices adaptés à ton niveau", "Un mode examen pour te préparer efficacement"], "bottomText": "<PERSON>a ne te prendra que 2 minutes !", "signature": "À très bientô<PERSON> sur Dinobot,", "team": "L'équipe <PERSON>"}, "2": {"preview": "Comment Léa a progressé de 3 points gr<PERSON>ce à Dinobot 📈", "subject": "Comment Léa a progressé de 3 points gr<PERSON>ce à Dinobot 📈", "greeting": "Salut ,", "intro": "Je voulais te partager l'histoire de Léa, él<PERSON>ve de Terminale S :\n\n\"<PERSON><PERSON>, je gal<PERSON>is en maths. <PERSON><PERSON>ant, je peux poser toutes mes questions au chatbot, refaire des exercices jusqu'à comprendre, et je me sens vraiment prête pour le bac. Ma moyenne est passée de 11 à 14 !\"\n\nEt si c'était ton tour de progresser ? Tu peux encore finaliser ton inscription :", "buttonText": "<PERSON> rejoins Dinobot maintenant →", "mainTitle": "Ce qui t'attend :", "features": ["Un prof virtuel disponible 24h/24 pour répondre à tes questions", "Des exercices qui s'adaptent à tes difficultés", "Tous les annales d'examen avec corrections détaillées"], "bottomText": "L'inscription prend 2 minutes et tu peux commencer tout de suite.", "signature": "<PERSON><PERSON>,", "team": "L'équipe <PERSON>", "ps": "P.S. : Tu peux aussi tester notre mode avatar où le chatbot te parle vraiment ! 🎥"}, "3": {"preview": "Dernière chance : on t'aide à finaliser ton inscription", "subject": "Dernière chance : on t'aide à finaliser ton inscription", "greeting": "Salut ,", "intro": "Ça fait une semaine que tu as commencé ton inscription sur Dinobot, et on ne voudrait pas que tu passes à côté !\n\nOn sait que parfois, s'inscrire sur une nouvelle plateforme peut sembler compliqué.\n\nC'est pourquoi on te propose :", "buttonText": "Finaliser rapidement →", "mainTitle": "Pourquoi ça vaut le coup ?", "features": ["Imagine pouvoir poser n'importe quelle question sur tes cours et avoir une réponse claire immédiatement", "Plus de blocages sur tes devoirs", "Plus de stress avant les contrôles"], "bottomText": "Ou nous écrire si tu as des questions : <EMAIL>", "signature": "On espère te voir bientôt sur Dinobot !", "team": "L'équipe <PERSON>", "unsubscribe": true, "extraButton": "Je me lance maintenant →"}}, "teacher": {"1": {"preview": "Finalisez votre inscription <PERSON>bot en 2 minutes", "subject": "Finalisez votre inscription <PERSON>bot en 2 minutes", "greeting": "Bonjou<PERSON> ,", "intro": "Vous avez commencé votre inscription sur <PERSON>bot hier. Il ne vous reste qu'une étape pour accéder à la plateforme et commencer à créer vos classes.", "buttonText": "Finaliser mon inscription →", "mainTitle": "Une fois inscrit(e), vous pourrez :", "features": ["<PERSON><PERSON><PERSON> et gérer vos classes", "Générer des contrôles personnalisés", "Suivre les progrès de vos élèves"], "bottomText": "", "signature": "Cordialement,", "team": "L'équipe <PERSON>", "secondButton": "Accéder à ma plateforme →"}, "2": {"preview": "\"Mes élèves sont plus autonomes\" - Témoignage Prof", "subject": "\"Mes élèves sont plus autonomes\" - Témoignage Prof", "greeting": "Bonjou<PERSON> ,", "intro": "<PERSON>, professeure de mathématiques au lycée <PERSON>, nous a partagé son retour :\n\n\"Depuis que j'utilise <PERSON>, mes élèves viennent en cours mieux préparés. Ils peuvent s'exercer à leur rythme, et moi je peux me concentrer sur l'essentiel en classe. Les contrôles générés automatiquement me font gagner un temps précieux !\"\n\nVous aussi, vous pouvez transformer votre façon d'enseigner :", "buttonText": "Finaliser mon inscription →", "mainTitle": "Les avantages pour vos classes :", "features": ["Suivi individualisé de chaque élève", "Génération automatique d'exercices et contrôles", "Correction assistée pour gagner du temps", "Tableau de bord des progrès de la classe"], "bottomText": "", "signature": "Cordialement,", "team": "L'équipe <PERSON>", "secondButton": "Découvrir la plateforme →"}, "3": {"preview": "Besoin d'aide pour votre inscription <PERSON>bot ?", "subject": "Besoin d'aide pour votre inscription <PERSON>bot ?", "greeting": "Bonjou<PERSON> ,", "intro": "Votre inscription sur Dinobot est restée en suspens depuis une semaine.\n\nRen<PERSON>trez-vous une difficulté particulière ?\n\nNous comprenons qu'adopter un nouvel outil pédagogique représente un investissement en temps. C'est pourquoi nous vous proposons :", "buttonText": "Finaliser mon inscription →", "mainTitle": "Ou programmer un appel de 15 minutes avec notre équipe pédagogique pour :", "features": ["Découvrir concrètement la plateforme", "Voir comment l'intégrer dans vos cours", "Répond<PERSON> à toutes vos questions"], "bottomText": "Alternative : Écrivez-nous à <EMAIL>\n\nNous sommes là pour vous accompagner dans la réussite de vos élèves.", "signature": "Cordialement,", "team": "L'équipe <PERSON>", "secondButton": "Réserver mon créneau →", "unsubscribe": true}}}, "common": {"followUs": "Suivez-nous !", "company": "OuiActive", "address": "Siège social : 166 Av. <PERSON>, 69003 Lyon", "legalNotice": "Mentions légales", "dataProtection": "Protection des données", "automaticEmail": "Ceci est un email automatique, merci de ne pas répondre", "unsubscribeText": "Ne plus recevoir ces emails :", "unsubscribeLink": "se d<PERSON>ab<PERSON>ner"}, "requestExtensionResponse": {"greeting": "Bonjour {prenom} {nom},", "detailsTitle": "<PERSON><PERSON><PERSON> de votre demande :", "subjectsLabel": "Matières/niveaux demandés :", "dateLabel": "Date de la demande :", "statusLabel": "Statut :", "buttonText": "Accéder à mon espace professeur →", "finalText": "Pour consulter l'historique de vos demandes, rendez-vous dans votre espace professeur > Mes demandes.", "signature": "Cordialement,", "team": "L'équipe <PERSON>", "ACCEPTED": {"preview": "Votre demande d'extension a été approuvée - Dinobot", "subject": "✅ Votre demande d'extension a été approuvée - Dinobot", "mainTitle": "Nous avons le plaisir de vous informer que votre demande d'extension de matières et niveaux sur la plateforme Dinobot a été approuvée.", "statusLabel": "Approu<PERSON><PERSON>", "commentLabel": "Commentaire de l'administrateur :", "bottomText": "Vos nouveaux droits d'accès sont désormais actifs. V<PERSON> pouvez dès à présent :", "benefits": ["<PERSON><PERSON><PERSON> et gérer vos classes pour ces nouvelles matières/niveaux", "Générer des contrôles adaptés", "Accéder aux ressources pédagogiques correspondantes"]}, "REJECTED": {"preview": "Votre demande d'extension a été refusée - Dinobot", "subject": "❌ Votre demande d'extension a été refusée - Dinobot", "mainTitle": "Nous vous informons que votre demande d'extension de matières et niveaux sur la plateforme Dinobot a été refusée.", "statusLabel": "<PERSON><PERSON><PERSON><PERSON>", "commentLabel": "<PERSON><PERSON><PERSON> du refu<PERSON> :", "bottomText": "Si vous souhaitez obtenir plus d'informations sur cette décision ou soumettre une nouvelle demande avec des éléments complémentaires, n'hésitez pas à nous contacter."}}}
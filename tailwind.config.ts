/** @type {import('tailwindcss').Config} */
module.exports = {
    darkMode: ['class'],
    content: [
        './pages/**/*.{ts,tsx}',
        './components/**/*.{ts,tsx}',
        './app/**/*.{ts,tsx}',
        './src/**/*.{ts,tsx}'
    ],
    prefix: '',
    theme: {
        container: {
            center: true,
            padding: '2rem',
            screens: {
                '2xl': '1400px'
            }
        },
        extend: {
            screens: {
                subfhd: { raw: '(min-height: 768px)' }
            },
            fontFamily: {
                sans: ['var(--font-geist-sans)'],
                mono: ['var(--font-geist-mono)'],
                inter: ['var(--font-inter)'],
                montserrat: ['var(--font-montserrat)']
            },
            colors: {
                dinoBotWhite: '#F5F5F5',
                dinoBotBlue: '#1861B0',
                dinoBotBlackBlue: '#1B264C',
                dinoBotVibrantBlue: '#0079FC',
                dinoBotSky: '#2AAFFB',
                dinoBotLightSky: '#BEE8FF',
                dinoBotLightBlue: '#D6EAFF',
                dinoBotRed: '#E22351',
                dinoBotYellow: '#FFB353',
                dinoBotCyan: '#15B596',
                dinoBotGreen: '#5DC140',
                dinoBotPurple: '#9E3ADB',
                dinoBotDarkPurple: '#6600D5',
                dinoBotRoseBonbon: '#FF55A2',
                dinoBotVividOrange: '#FF5E0E',
                dinoBotRedOrange: '#FF7250',
                dinoBotDarkGray: '#5C5C5C',
                dinoBotGray: '#707070',
                dinoBotLightGray: '#D9D9D9',
                border: 'hsl(var(--border))',
                input: 'hsl(var(--input))',
                ring: 'hsl(var(--ring))',
                background: 'hsl(var(--background))',
                foreground: 'hsl(var(--foreground))',
                primary: {
                    DEFAULT: 'hsl(var(--primary))',
                    foreground: 'hsl(var(--primary-foreground))'
                },
                secondary: {
                    DEFAULT: 'hsl(var(--secondary))',
                    foreground: 'hsl(var(--secondary-foreground))'
                },
                destructive: {
                    DEFAULT: 'hsl(var(--destructive))',
                    foreground: 'hsl(var(--destructive-foreground))'
                },
                muted: {
                    DEFAULT: 'hsl(var(--muted))',
                    foreground: 'hsl(var(--muted-foreground))'
                },
                accent: {
                    DEFAULT: 'hsl(var(--accent))',
                    foreground: 'hsl(var(--accent-foreground))'
                },
                popover: {
                    DEFAULT: 'hsl(var(--popover))',
                    foreground: 'hsl(var(--popover-foreground))'
                },
                card: {
                    DEFAULT: 'hsl(var(--card))',
                    foreground: 'hsl(var(--card-foreground))'
                }
            },
            borderRadius: {
                lg: 'var(--radius)',
                md: 'calc(var(--radius) - 2px)',
                sm: 'calc(var(--radius) - 4px)'
            },
            transitionDuration: {
                '0': '0ms',
                '2000': '2000ms',
                '5000': '5000ms'
            },
            keyframes: {
                'accordion-down': {
                    from: { height: '0' },
                    to: { height: 'var(--radix-accordion-content-height)' }
                },
                'accordion-up': {
                    from: { height: 'var(--radix-accordion-content-height)' },
                    to: { height: '0' }
                },
                'infinite-rotation': {
                    '0%': {
                        transform: 'rotate(0)'
                    },
                    '100%': {
                        transform: 'rotate(360deg)'
                    }
                },
                'fade-in': {
                    '0%': {
                        opacity: '0'
                    },
                    '100%': {
                        opacity: '1'
                    }
                },
                'fade-out': {
                    '0%': {
                        opacity: '1'
                    },
                    '100%': {
                        opacity: '0'
                    }
                },
                'fade-in-down': {
                    '0%': {
                        opacity: '0',
                        transform: 'translateY(-20px)'
                    },
                    '100%': {
                        opacity: '1',
                        transform: 'translateY(0)'
                    }
                },
                'fade-in-left': {
                    from: {
                        opacity: '0',
                        transform: 'translateX(-20px)'
                    },
                    to: {
                        opacity: '1',
                        transform: 'translateX(0px)'
                    }
                },
                'fade-in-up': {
                    '0%': {
                        opacity: '0',
                        transform: 'translateY(20px)'
                    },
                    '100%': {
                        opacity: '1',
                        transform: 'translateY(0)'
                    }
                },
                'fade-in-right': {
                    from: {
                        opacity: '0',
                        transform: 'translateX(20px)'
                    },
                    to: {
                        opacity: '1',
                        transform: 'translateX(0px)'
                    }
                },
                'fade-in-top-left': {
                    from: {
                        opacity: '0',
                        transform: 'translate(-20px, -20px)'
                    },
                    to: {
                        opacity: '1',
                        transform: 'translate(0px, 0px)'
                    }
                },
                'fade-in-top-right': {
                    from: {
                        opacity: '0',
                        transform: 'translate(20px, -20px)'
                    },
                    to: {
                        opacity: '1',
                        transform: 'translate(0px, 0px)'
                    }
                },
                'fade-in-bottom-left': {
                    from: {
                        opacity: '0',
                        transform: 'translate(-20px, 20px)'
                    },
                    to: {
                        opacity: '1',
                        transform: 'translate(0px, 0px)'
                    }
                },
                'fade-in-bottom-right': {
                    from: {
                        opacity: '0',
                        transform: 'translate(20px, 20px)'
                    },
                    to: {
                        opacity: '1',
                        transform: 'translate(0px, 0px)'
                    }
                },
                'fade-out-up': {
                    '0%': {
                        opacity: '1',
                        transform: 'translateY(0px)'
                    },
                    '100%': {
                        opacity: '0',
                        transform: 'translateY(20px)'
                    }
                },
                'fade-out-down': {
                    '0%': {
                        opacity: '1',
                        transform: 'translateY(0px)'
                    },
                    '100%': {
                        opacity: '0',
                        transform: 'translateY(-20px)'
                    }
                },
                'fade-out-right': {
                    from: {
                        opacity: '1',
                        transform: 'translateX(0px)'
                    },
                    to: {
                        opacity: '0',
                        transform: 'translateX(20px)'
                    }
                },
                'fade-out-left': {
                    from: {
                        opacity: '1',
                        transform: 'translateX(0px)'
                    },
                    to: {
                        opacity: '0',
                        transform: 'translateX(-20px)'
                    }
                },
                'minified-fade-out-up': {
                    from: {
                        position: 'absolute',
                        opacity: '1',
                        transform: 'translateX(0px) scale(1)'
                    },
                    to: {
                        position: 'absolute',
                        opacity: '0',
                        transform: 'translateY(-500px) scale(0.1)'
                    }
                },
                wiggle: {
                    '0%, 100%': { transform: 'rotate(-2deg)' },
                    '50%': { transform: 'rotate(2deg)' }
                },
                moveAround: {
                    '0%': { transform: 'translate(0, 0)', fontSize: '16px' },
                    '25%': {
                        transform: 'translate(-800px, -800px)',
                        fontSize: '10px'
                    },
                    '50%': {
                        transform: 'translate(800px, -400px)',
                        fontSize: '30px'
                    },
                    '75%': {
                        transform: 'translate(-200px, -1000px)',
                        fontSize: '10px'
                    },
                    '100%': {
                        transform: 'translate(1000px, 1000px)',
                        fontSize: '20px'
                    }
                },
                pageHighlight: {
                    '0%, 100%': { boxShadow: 'inset 0 0 0 hsla(var(--page-highlight) / 0)' },
                    '50%': { boxShadow: 'inset 0 0 50px hsla(var(--page-highlight) / 0.9)' },
                },
            },
            animation: {
                'accordion-down': 'accordion-down 0.2s ease-out',
                'accordion-up': 'accordion-up 0.2s ease-out',
                'infinite-rotation': 'infinite-rotation 0.7s ease-out infinite',
                'fade-in': 'fade-in 0.5s ease-out',
                'fade-out': 'fade-out 0.5s ease-out',
                'fade-in-down': 'fade-in-down 0.5s ease-out',
                'fade-in-left': 'fade-in-left 0.5s ease-out',
                'fade-in-up': 'fade-in-up 0.5s ease-out',
                'fade-in-right': 'fade-in-right 0.5s ease-out',
                'fade-in-top-left': 'fade-in-top-left 0.5s ease-out',
                'fade-in-top-right': 'fade-in-top-right 0.5s ease-out',
                'fade-in-bottom-left': 'fade-in-bottom-left 0.5s ease-out',
                'fade-in-bottom-right': 'fade-in-bottom-right 0.5s ease-out',
                'fade-out-down': 'fade-out-down 0.3s ease-out',
                'fade-out-left': 'fade-out-left 0.3s ease-out',
                'fade-out-up': 'fade-out-up 0.3s ease-out',
                'fade-out-right': 'fade-out-right 0.3s ease-out',
                'minified-fade-out-up': 'minified-fade-out-up 0.7s ease-out',
                wiggle: 'wiggle 1s ease-in-out infinite',
                moveAround: 'moveAround 1s ease-in-out infinite',
                pageHighlight: 'pageHighlight 5s ease-in-out infinite'
            },
            backgroundImage: {
                'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))'
            }
        }
    },
    plugins: [
        require('tailwindcss-animate'),
        require('@tailwindcss/typography')
    ]
}

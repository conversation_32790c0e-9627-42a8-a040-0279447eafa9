import { z } from 'zod';
import { PartWithRelationsSchema, PartPartialWithRelationsSchema, PartOptionalDefaultsWithRelationsSchema } from './PartSchema'
import type { PartWithRelations, PartPartialWithRelations, PartOptionalDefaultsWithRelations } from './PartSchema'
import { PraxeoTaskSkillWithRelationsSchema, PraxeoTaskSkillPartialWithRelationsSchema, PraxeoTaskSkillOptionalDefaultsWithRelationsSchema } from './PraxeoTaskSkillSchema'
import type { PraxeoTaskSkillWithRelations, PraxeoTaskSkillPartialWithRelations, PraxeoTaskSkillOptionalDefaultsWithRelations } from './PraxeoTaskSkillSchema'

/////////////////////////////////////////
// TM SKILLS SCHEMA
/////////////////////////////////////////

export const TmSkillsSchema = z.object({
  id: z.string(),
  description: z.string().nullable(),
  partId: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
})

export type TmSkills = z.infer<typeof TmSkillsSchema>

/////////////////////////////////////////
// TM SKILLS PARTIAL SCHEMA
/////////////////////////////////////////

export const TmSkillsPartialSchema = TmSkillsSchema.partial()

export type TmSkillsPartial = z.infer<typeof TmSkillsPartialSchema>

/////////////////////////////////////////
// TM SKILLS OPTIONAL DEFAULTS SCHEMA
/////////////////////////////////////////

export const TmSkillsOptionalDefaultsSchema = TmSkillsSchema.merge(z.object({
  id: z.string().optional(),
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
}))

export type TmSkillsOptionalDefaults = z.infer<typeof TmSkillsOptionalDefaultsSchema>

/////////////////////////////////////////
// TM SKILLS RELATION SCHEMA
/////////////////////////////////////////

export type TmSkillsRelations = {
  part: PartWithRelations;
  praxeoTaskSkills: PraxeoTaskSkillWithRelations[];
};

export type TmSkillsWithRelations = z.infer<typeof TmSkillsSchema> & TmSkillsRelations

export const TmSkillsWithRelationsSchema: z.ZodType<TmSkillsWithRelations> = TmSkillsSchema.merge(z.object({
  part: z.lazy(() => PartWithRelationsSchema),
  praxeoTaskSkills: z.lazy(() => PraxeoTaskSkillWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// TM SKILLS OPTIONAL DEFAULTS RELATION SCHEMA
/////////////////////////////////////////

export type TmSkillsOptionalDefaultsRelations = {
  part: PartOptionalDefaultsWithRelations;
  praxeoTaskSkills: PraxeoTaskSkillOptionalDefaultsWithRelations[];
};

export type TmSkillsOptionalDefaultsWithRelations = z.infer<typeof TmSkillsOptionalDefaultsSchema> & TmSkillsOptionalDefaultsRelations

export const TmSkillsOptionalDefaultsWithRelationsSchema: z.ZodType<TmSkillsOptionalDefaultsWithRelations> = TmSkillsOptionalDefaultsSchema.merge(z.object({
  part: z.lazy(() => PartOptionalDefaultsWithRelationsSchema),
  praxeoTaskSkills: z.lazy(() => PraxeoTaskSkillOptionalDefaultsWithRelationsSchema).array(),
}))

/////////////////////////////////////////
// TM SKILLS PARTIAL RELATION SCHEMA
/////////////////////////////////////////

export type TmSkillsPartialRelations = {
  part?: PartPartialWithRelations;
  praxeoTaskSkills?: PraxeoTaskSkillPartialWithRelations[];
};

export type TmSkillsPartialWithRelations = z.infer<typeof TmSkillsPartialSchema> & TmSkillsPartialRelations

export const TmSkillsPartialWithRelationsSchema: z.ZodType<TmSkillsPartialWithRelations> = TmSkillsPartialSchema.merge(z.object({
  part: z.lazy(() => PartPartialWithRelationsSchema),
  praxeoTaskSkills: z.lazy(() => PraxeoTaskSkillPartialWithRelationsSchema).array(),
})).partial()

export type TmSkillsOptionalDefaultsWithPartialRelations = z.infer<typeof TmSkillsOptionalDefaultsSchema> & TmSkillsPartialRelations

export const TmSkillsOptionalDefaultsWithPartialRelationsSchema: z.ZodType<TmSkillsOptionalDefaultsWithPartialRelations> = TmSkillsOptionalDefaultsSchema.merge(z.object({
  part: z.lazy(() => PartPartialWithRelationsSchema),
  praxeoTaskSkills: z.lazy(() => PraxeoTaskSkillPartialWithRelationsSchema).array(),
}).partial())

export type TmSkillsWithPartialRelations = z.infer<typeof TmSkillsSchema> & TmSkillsPartialRelations

export const TmSkillsWithPartialRelationsSchema: z.ZodType<TmSkillsWithPartialRelations> = TmSkillsSchema.merge(z.object({
  part: z.lazy(() => PartPartialWithRelationsSchema),
  praxeoTaskSkills: z.lazy(() => PraxeoTaskSkillPartialWithRelationsSchema).array(),
}).partial())

export default TmSkillsSchema;

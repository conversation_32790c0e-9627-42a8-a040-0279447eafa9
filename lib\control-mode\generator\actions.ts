'use server'
import { auth } from '@/auth'
import { logger } from '@/logger/logger'
import { z } from 'zod'
import { generateWithAi as generateWithAiCore } from '@/lib/ai/llm/generator'
import {
    getAllPartsOfChapter,
    getChapterFromPartId,
    getPartsByChapterId,
    getRandomizedQuestion
} from '../../training-mode/actions'
import {
    ControlFeedbackOutput,
    feedbackOutputSchema
} from '../types/control/types'
import { getExercise } from '@/lib/exams/actions'
import { sendFileToMathpixOcr, sendFileToMistralOcr } from '@/lib/training/actions'
import { getChapterById } from '@/lib/chapters/actions'
import { getTranslations } from 'next-intl/server'
import { fillPrompt, formatListToString } from '@/lib/utils/utils'
import { Session } from 'next-auth'
import { DesmosSchema } from '@/lib/desmos/types'
import {
    PromptFeatureType,
    SystemPromptService
} from '@/lib/ai/prompts/system-prompt-service'
import { EvaluationPrompts } from '@/lib/ai/prompts/evaluation/types'
import { buildPrompt } from '@/lib/ai/prompts/prompt-builder'
import { FormattingPrompts } from '@/lib/ai/prompts/other/formatting-rules'
import { fetchProviderAndModelByTask } from '@/lib/ai/llm/server'
import { LlmModelPartial } from '@/prisma/generated/zod/modelSchema/LlmModelSchema'
import Handlebars from '@/lib/utils/handlebars.utils'
import { fetchPraxeosByChapterId } from '@/lib/praxeo/server'
import { PraxeoType } from '@/lib/types/praxeo.type'
import { fetchSkillsByIds } from '@/lib/skills/server'
import { getRandomTasksByChapterId } from '../server'
const documentOcr = process.env.DOCUMENT_OCR ?? 'mistral'
const TEACHER_EVALUATION = 'Professeur / Évaluation'
const STUDENT_EVALUATION = 'Élève / Évaluation'
// This is just an example, just for dev

// questions input : questionsContent, questionsContentType, solutionContent, questionTitle, domain, level, chapter, lastResult, examFile, solutionFile, questionsNumber , execisesNumber
export interface QuestionsModeGeneratorInput {
    questionsContentsList?: string[]
    quesionsContentType?: string
    solutionsContentsList?: string[]
    questionsTitlesList?: string[]
    domain?: string
    level?: string
    chapter?: string
    lastResult?: string
    examFile?: string
    solutionFile?: string
    questionsNumber?: number
    execisesNumber?: number
    generateSolutionOnly?: boolean
}

export interface ControlModeGeneratorInput {
    llmModel?: string
    chapterId?: string
    partId?: string
    domain?: string
    exerciseId?: string
    quesIds?: string[]
    file?: {
        filename?: string
        data: string
    }
    solutionFile?: {
        filename?: string
        data: string
    }
    customQuestion?: ExerciseSchema
}

export type ExerciseSchema = {
    questionContent: string
    contentType: 'text' | 'html' | 'latex'
    solution?: string
    desmosCode: z.infer<typeof DesmosSchema>
    questionType: 'qcm' | 'text'
}

export type ControleModeGeneratorOutput = ExerciseSchema[]

const llmOutputRecipe = z.object({
    output: z.array(
        z.array(
            z.object({
                description: z
                    .string()
                    .describe('Type de tâches (T) au sens praxeologique'),
                questionContent: z
                    .string()
                    .describe(
                        'The question content, make sure to respect user language !'
                    ),
                contentType: z
                    .enum(['text', 'html', 'latex'])
                    .describe(
                        'Le type de formatage du contenu de la question.'
                    ),
                desmosCode: DesmosSchema,
                solution: z
                    .string()
                    .describe(
                        'Une solution brève à la question, avec un niveau de détails modéré.'
                    ),
                questionType: z
                    .enum(['qcm', 'text'])
                    .describe(
                        'Le type de question, soit choix multiple soit texte.'
                    )
            })
        )
    ),
    llmComment: z.string().optional()
        .describe(`Ce message/commentaire à destination du backend indique s'il y a des problèmes avec la consigne ou les données fournies.
               Il sera effacé avant d'être envoyé au frontend.`)
})

const printInput = (input: ControlModeGeneratorInput): string => {
    const { file, ...rest } = input
    const sanitizedInput = {
        ...rest,
        file: file ? { filename: file.filename } : undefined,
        solutionFile: file ? { filename: file.filename } : undefined
    }
    return JSON.stringify(sanitizedInput, null, 2)
}

/**
 * Generates training mode exercises based on the given input.
 *
 * @param input - The input for generating training mode exercises.
 * @returns A promise that resolves to an array of training mode exercises or null if generation fails.
 */
const getControleContent = async (
    input: ControlModeGeneratorInput
): Promise<{
    domain?: string
    questionTitle?: string
    questionContent?: string
    contentTask?: {
        description: string
        competencies: (string | null)[]
    }[]
    questionContentType: string
    solutionContent?: string
    lastResult?: string
    examFile?: string
    solutionFile?: string
    level?: string
    parts?: string
    chapter?: string
    questionsNumber?: number
}> => {
    if (input.chapterId) {
        const chapter = await getChapterById(input.chapterId)
        const randomQuestions = await getRandomizedQuestion({
            chapterId: input.chapterId
        })

        if (!randomQuestions) {
            throw new Error(
                'Failed to get random question for training mode exercises generation'
            )
        }

        const chapterName = chapter?.title || ''
        const parts = await getAllPartsOfChapter(input.chapterId)
        const tasks = await getRandomTasksByChapterId(input.chapterId)

        if (!parts) {
            logger.warn(`No Parts found for the chapter: ${chapterName}`)
        }

        if (!tasks) {
            logger.warn(`No Tasks found for the chapter: ${chapterName}`)
        }

        return {
            domain: chapter?.domain?.name || '',
            level: chapter?.level?.name || '',
            chapter: chapter?.title || '',
            questionTitle: chapterName || '',
            contentTask: tasks,
            parts,
            questionContentType: 'latex'
        }
    } else if (input.partId) {
        const chapter = await getChapterFromPartId(input.partId)
        const randomQuestions = await getRandomizedQuestion({
            partId: input.partId
        })

        if (!randomQuestions) {
            throw new Error(
                'Failed to get random question for training mode exercises generation'
            )
        }

        const concatenatedQuestions = randomQuestions
            .map(question => question.content)
            .join('\n\n')
        const concatenatedQuestionTitles = randomQuestions
            .map(question => question.knowledge)
            .join('\n\n')

        return {
            domain: chapter.domain?.name || '',
            questionTitle: concatenatedQuestionTitles,
            questionContent: concatenatedQuestions,
            questionContentType:
                randomQuestions.length > 0
                    ? randomQuestions[0].contentType
                    : 'latex'
        }
    } else if (input.exerciseId) {
        const exercise = await getExercise(input.exerciseId)

        if (!exercise) {
            logger.error(
                `Failed to get exam for training mode exercises generation, id: ${input.exerciseId}`
            )
            throw new Error(
                'Failed to get exercise for training mode exercises generation'
            )
        }

        return {
            questionTitle: exercise.title || '',
            questionContent: exercise.assignment || '',
            questionContentType: 'latex'
        }
    } else if (input.file) {
        if (!input.file.data) {
            logger.error(
                `Invalid input for exercise generation: ${printInput(input)}`
            )
            throw new Error('Invalid input for exercise generation')
        }

        let questionContent = ''

        try {
            logger.debug(
                `Sending file to Mathpix OCR for content extraction: ${input.file.filename || 'file'}`
            )
            questionContent = (documentOcr === 'mistral'
                ? await sendFileToMistralOcr(
                    input.file.filename || 'file',
                    'pdf',
                    input.file.data
                )
                :
                await sendFileToMathpixOcr(
                    input.file.filename || 'file',
                    'pdf',
                    input.file.data
                )
            ).text
        } catch {
            logger.error(
                `Failed to extract content from file: ${input.file.filename || 'file'}`
            )
        }

        return {
            questionContent,
            questionContentType: 'latex',
            examFile: input.file.data,
            solutionFile: input.solutionFile?.data
        }
    } else {
        throw new Error('Invalid input for exercise generation')
    }
}

//generate questions based on the given ones
//input : questionsTitlesList, questionsContentsList, solutionsContentsList
export const generateQuestions = async (
    input: QuestionsModeGeneratorInput
): Promise<ControleModeGeneratorOutput[] | null> => {
    'use server'
    //TODO: use middleware to check if the user is authenticated instead of using the session each time
    const session = await auth()

    const t = await getTranslations('lib.prompts')
    logger.debug(`[GENERATE_CONTROL] lang : ${t(`lang`)}`)

    if (!session?.user) {
        throw new Error('User not logged in')
    }

    try {
        logger.debug('Generating questions with input: ' + input)

        const {
            questionsContentsList,
            quesionsContentType,
            solutionsContentsList,
            questionsTitlesList,
            domain,
            level,
            chapter,
            lastResult,
            examFile,
            solutionFile,
            questionsNumber,
            execisesNumber,
            generateSolutionOnly // New parameter
        } = input

        const evalPrompt = (await SystemPromptService.getSystemPrompt(
            PromptFeatureType.EVALUATION
        )) as EvaluationPrompts
        const formattingPrompt = (await SystemPromptService.getSystemPrompt(
            PromptFeatureType.FORMATTING
        )) as FormattingPrompts

        const userType = session.user.type
        const providerAndModel = await fetchProviderAndModelByTask(
            userType == 'student' ? STUDENT_EVALUATION : TEACHER_EVALUATION
        )

        const questionTitle = formatListToString(questionsTitlesList)
        const questionContent = formatListToString(questionsContentsList)
        const solutionContent = formatListToString(solutionsContentsList)

        // Choose appropriate prompt based on generation type
        const promptTemplate = generateSolutionOnly
            ? evalPrompt.solutionGenerationPromptRules
            : evalPrompt.controlModePromptRules

        const questionsPromptInput = {
            questionContent,
            quesionsContentType: quesionsContentType!,
            solutionContent,
            questionTitle,
            domain: domain!,
            level: level!,
            chapter: chapter!,
            lastResult: lastResult!,
            examFile: examFile!,
            solutionFile: solutionFile!,
            questionNumber: questionsNumber?.toString(),
            exerciseNumber: execisesNumber?.toString(),
            lang: t(`lang`)
        }

        const mainPrompt = await buildPrompt({
            customPrompt: promptTemplate.prompt,
            customMarkdownRules: formattingPrompt.markdownRules.prompt,
            customkatexRules: formattingPrompt.katexRules.prompt,
            needsMath: true,
            needsDesmos: true,
            needsChemistry: true
        })

        const filledPrompt = fillPrompt(mainPrompt, questionsPromptInput)

        logger.debug('Prompt: \n' + filledPrompt)

        return await generateWithAi(
            session,
            filledPrompt,
            providerAndModel.profile?.name,
            providerAndModel.model
        )
    } catch (error) {
        logger.error('Failed to generate questions :' + error)
        return null
    }
}

// ask directly the ai
// input : prompt
// session: choses the LLm according to the user
export const generateWithAi = async (
    session: Session | null,
    prompt: string,
    providerName?: string,
    modelName?: string
): Promise<ControleModeGeneratorOutput[] | null> => {
    'use server'
    try {
        const t = await getTranslations('lib.prompts')
        logger.debug(`[GENERATE_CONTROL] lang : ${t(`lang`)}`)

        const exercises = await generateWithAiCore({
            user: session?.user ?? null,
            prompt,
            systemPrompt: `IMPORTANT: Make sure that the content generated is in this language : ${t(`lang`)}`,
            llmOutputRecipe,
            feature: PromptFeatureType.EVALUATION,
            providerName,
            modelName
        })

        return (exercises?.output as ControleModeGeneratorOutput[]) ?? null
    } catch (error) {
        logger.error(`Failed to call ai because: ${error}`)
        return null
    }
}

export const generateControle = async (
    input: ControlModeGeneratorInput
): Promise<ControleModeGeneratorOutput[] | null> => {
    'use server'
    const session = await auth()

    const t = await getTranslations('lib.prompts')
    logger.debug(`[GENERATE_CONTROL] lang : ${t(`lang`)}`)

    if (!session?.user) {
        throw new Error('User not logged in')
    }

    try {
        logger.debug(
            `Generating control mode exercises with input: ${printInput(input)}`
        )

        const { contentTask, domain, level, chapter, parts } =
            await getControleContent(input)

        //   const regenerationInstructions = input.lastResult ?
        //     `Ceci est une régénération. Utilisez le résultat précédent comme référence, mais créez des exercices DIFFÉRENTS. Maintenez le même niveau de difficulté et le même type de contenu que les exercices originaux. Assurez-vous que les nouvelles questions sont uniques et non des copies des précédentes.

        //     Résultat précédent à modifier : ${lastResult}` :
        //     '';

        // const questionNumber: string = input.chapterId ? process.env.CONTROL_MODE_QUESTION_NUMBER as string || "4" : input.customQuestion ? "1" : "3";
        const exerciseNumber: string = input.chapterId
            ? (process.env.CONTROL_MODE_EXERCISE_NUMBER as string) || '3'
            : '1'

        // TODO change Prompte to asined us prompte
        const controlPrompt = (await SystemPromptService.getSystemPrompt(
            PromptFeatureType.EVALUATION
        )) as EvaluationPrompts
        const formattingPrompt = (await SystemPromptService.getSystemPrompt(
            PromptFeatureType.FORMATTING
        )) as FormattingPrompts
        const prompt = await buildPrompt({
            customMarkdownRules: formattingPrompt.markdownRules.prompt,
            customkatexRules: formattingPrompt.katexRules.prompt,
            customPrompt: controlPrompt.controlModePromptRules.prompt,
            topic: domain
        })
        // TODO change to handlebars provider
        const filledPrompt = Handlebars.compile(prompt)({
            selectedTask: contentTask,
            domain,
            level,
            parts,
            chapter,
            difficultyLevel: Math.floor(Math.random() * 3) + 1,
            exerciseNumber
        })

        const userType = session.user.type

        const providerAndModel = await fetchProviderAndModelByTask(
            userType == 'student' ? STUDENT_EVALUATION : TEACHER_EVALUATION
        )

        const { model: modelName, profile } = providerAndModel
        const { name: providerName } = profile as LlmModelPartial

        const exercises = await generateWithAiCore({
            user: session?.user ?? null,
            prompt: filledPrompt,
            systemPrompt: `IMPORTANT: Make sure that the content generated is in this language : ${t(`lang`)}`,
            llmOutputRecipe,
            feature: PromptFeatureType.EVALUATION,
            functionName: 'generateEvaluation',
            providerName,
            modelName
        })
        // Erase the message to the backend before sending to the frontend
        if (exercises) {
            exercises.llmComment = ''
        }

        return exercises?.output as ControleModeGeneratorOutput[]
    } catch (error) {
        logger.error('Failed to generate or parse exercises:' + error)
        return null
    }
}

type generateControleByFileParams = {
    formFiles: FormData
    domainName: string
    levelName: string
    isCourse: boolean
    documentType: 'cours' | 'exercice'
}
export const generateControleByFile = async ({
    isCourse,
    domainName,
    formFiles,
    levelName,
    documentType
}: generateControleByFileParams) => {
    try {
        const session = await auth()
        const t = await getTranslations('lib.prompts')
        logger.debug(`[GENERATE_CONTROL] lang : ${t(`lang`)}`)
        const userType = session?.user?.type

        const providerAndModel = await fetchProviderAndModelByTask(
            userType == 'student' ? STUDENT_EVALUATION : TEACHER_EVALUATION
        )
        const EvalFilePrompt = (await SystemPromptService.getSystemPrompt(
            PromptFeatureType.EVALUATION
        )) as EvaluationPrompts
        const formattingPrompt = (await SystemPromptService.getSystemPrompt(
            PromptFeatureType.FORMATTING
        )) as FormattingPrompts
        const prompt = await buildPrompt({
            customMarkdownRules: formattingPrompt.markdownRules.prompt,
            customkatexRules: formattingPrompt.katexRules.prompt,
            customPrompt: EvalFilePrompt.evaluationModeFilePrompt.prompt,
            topic: domainName
        })
        const finalPrompt = Handlebars.compile(prompt)

        // Récupération des fichiers
        const file = formFiles.get('file') as File
        const solutionFile = formFiles.get('solutionFile') as File

        // Création du tableau de PDF en vérifiant d'abord si ce sont des objets File valides
        const pdfsArray: File[] = []
        if (file instanceof File && file.size > 0) pdfsArray.push(file)
        if (solutionFile instanceof File && solutionFile.size > 0)
            pdfsArray.push(solutionFile)

        const result = await generateWithAiCore({
            user: session?.user ?? null,
            prompt: finalPrompt({
                documentType,
                level: levelName,
                domain: domainName,
                correction:
                    !isCourse &&
                    solutionFile instanceof File &&
                    solutionFile.size > 0
            }),
            pdfs: pdfsArray,
            systemPrompt: `IMPORTANT: Make sure that the content generated is in this language : ${t(`lang`)}`,
            llmOutputRecipe,
            feature: PromptFeatureType.EVALUATION,
            functionName: 'generateControleByFile',
            providerName: providerAndModel.profile?.name,
            modelName: providerAndModel.model
        })
        return result?.output
    } catch (error) {
        logger.error(
            `Failed to generate Controle By File or parse exercises: ${error}`
        )
    }
}

export const generateFeedback = async (
    input: ControlFeedbackOutput
): Promise<ControlFeedbackOutput | null> => {
    'use server'
    const session = await auth()

    const t = await getTranslations('lib.prompts')
    logger.debug(`[GENERATE_CONTROL] lang : ${t(`lang`)}`)

    if (!session?.user) {
        throw new Error('User not logged in')
    }

    try {
        const evalPrompt = (await SystemPromptService.getSystemPrompt(
            PromptFeatureType.EVALUATION
        )) as EvaluationPrompts
        const formattingPrompt = (await SystemPromptService.getSystemPrompt(
            PromptFeatureType.FORMATTING
        )) as FormattingPrompts

        const mainPrompt = await buildPrompt({
            customPrompt: evalPrompt.feedbackGenerationPrompt.prompt,
            customMarkdownRules: formattingPrompt.markdownRules.prompt,
            customkatexRules: formattingPrompt.katexRules.prompt,
            needsMath: true,
            needsDesmos: true,
            needsChemistry: true
        })

        const filledPrompt = mainPrompt
            .replaceAll('{answers}', JSON.stringify(input))
            .replaceAll('{lang}', t(`lang`))

        logger.debug('Prompt: \n' + filledPrompt)

        const exercises = await generateWithAiCore({
            user: session?.user ?? null,
            prompt: filledPrompt,
            systemPrompt: `IMPORTANT: Make sure that the content generated is in this language : ${t(`lang`)}`,
            llmOutputRecipe: feedbackOutputSchema,
            feature: PromptFeatureType.EVALUATION,
            functionName: 'generateFeedback'
        })

        return exercises as ControlFeedbackOutput
    } catch (error) {
        logger.error('Failed to generate or parse exercises:' + error)
        return null
    }
}

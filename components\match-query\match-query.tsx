import { Query, ValueProp } from '@/lib/types/query.type'
import { ReactNode } from 'react'

export type MatchQueryProps<T, E = unknown> = ValueProp<Query<T, E>> & {
    error?: (error: E) => ReactNode
    loading?: () => ReactNode
    success?: (data: T) => ReactNode
    inactive?: () => ReactNode
    empty?: () => ReactNode
}

export function MatchQuery<T, E = unknown>({
    value,
    error = () => null,
    loading = () => null,
    success = () => null,
    inactive = () => null,
    empty = () => null
}: MatchQueryProps<T, E>) {
    if (value.data !== undefined) {
        if (Array.isArray(value.data) && value.data.length === 0) {
            return <>{empty()}</>
        }
        return <>{success(value.data)}</>
    }

    if (value.error) {
        return <>{error(value.error)}</>
    }

    if (value.isLoading) {
        return <>{loading()}</>
    }

    return <>{inactive()}</>
}

export type MatchQueryWrapperProps<T, E = unknown> = Pick<
    MatchQueryProps<T>,
    'success'
> &
    Partial<Pick<MatchQueryProps<T, E>, 'error' | 'loading' | 'inactive'>>

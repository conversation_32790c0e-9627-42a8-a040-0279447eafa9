type CacheKey = string | number

function withCache<Args extends unknown[], Return>(
    fn: (...args: Args) => Promise<Return>,
    getKey: (...args: Args) => CacheKey
): (...args: Args) => Promise<Return> {
    const cache = new Map<CacheKey, Promise<Return>>()

    return (...args: Args): Promise<Return> => {
        const key = getKey(...args)

        if (!cache.has(key)) {
            const result = fn(...args)
            cache.set(key, result)
        }
        return cache.get(key)!
    }
}

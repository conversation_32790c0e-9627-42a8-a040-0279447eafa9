import { auth, getUser } from '@/auth'
import { redirect } from '@/i18n/routing'
import { logOut } from '@/lib/user/log'
import { UserStatus } from '@prisma/client'
import { Session } from 'next-auth'
import { getLocale } from 'next-intl/server'
import ForceLogout from '../auth/ForceLogout'

function withCheckAuth<P extends object>() {
    return (WrappedComponent: React.ComponentProps<P & any>) => {
        const Component = async (props: P) => {
            const session = (await auth()) as Session
            const locale = await getLocale()
            if (!session?.user) {
                redirect({ href: '/login', locale })
                return null
            }
            const user = await getUser(session.user.email!)
            // If session exists, but user is not in DB (i.e., deleted), force logout.
            if (session.user && !user) {
                return <ForceLogout />
            }

            if (user && user.status !== UserStatus.PENDING_DELETION) {
                redirect({ href: '/', locale })
                return null
            }

            return <WrappedComponent {...props} />
        }
        Component.displayName = 'WithCheckAuth'
        return Component
    }
}

export default withCheckAuth

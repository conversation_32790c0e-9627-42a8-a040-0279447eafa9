/*
  Warnings:

  - A unique constraint covering the columns `[id,updated_by_id]` on the table `feature_flags` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "feature_flags" ADD COLUMN     "description" TEXT,
ADD COLUMN     "updated_by_id" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "feature_flags_id_updated_by_id_key" ON "feature_flags"("id", "updated_by_id");

-- AddForeignKey
ALTER TABLE "feature_flags" ADD CONSTRAINT "feature_flags_updated_by_id_fkey" FOREIGN KEY ("updated_by_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

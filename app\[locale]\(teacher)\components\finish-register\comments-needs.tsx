import { Button } from '@/components/ui/button'
import {
    <PERSON><PERSON>,
    <PERSON>alogContent,
    DialogDescription,
    <PERSON><PERSON>Footer,
    <PERSON><PERSON>Header,
    <PERSON>alogTitle,
    DialogTrigger
} from '@/components/ui/dialog'
import { useTranslations } from 'next-intl'
import React, { useState } from 'react'
import DomainsSelection from './domains-selection'
import LevelsSelection from './levels-selection'
import { selectRegisterStepsStore } from '../../store/register-steps.store'
import { cn } from '@/lib/utils/utils'
import { createRequestLevelDomain } from '@/lib/requests-level-domain/action'
import ExplanationStep from './explanation-step'
import { useMutation } from '@tanstack/react-query'
import { isEmptyOrNull } from '@/lib/utils/string.utils'
import { toast } from 'sonner'

const CommentsNeeds = () => {
    const t = useTranslations(`teacher`)
    const [open, setOpen] = useState(false)
    const step = selectRegisterStepsStore.use.stepRequest()
    const domainsLength = selectRegisterStepsStore.use.domainsNeeds().length
    const domains = selectRegisterStepsStore.use.domainsNeeds()
    const explanation = selectRegisterStepsStore.use.explanation()
    const levelDomains = selectRegisterStepsStore.use.levelDomainsNeeds()
    const {
        setStepRequest,
        setDomainsNeeds,
        setLevelDomainsNeeds,
        clearNeeds
    } = selectRegisterStepsStore.use.actions()
    const lengthLD = Array.from(levelDomains.values()).reduce(
        (acc, levels) => acc + levels.length,
        0
    )
    const { mutate } = useMutation({
        mutationKey: ['createRequestLevelDomain'],
        mutationFn: () =>
            createRequestLevelDomain(
                explanation,
                Array.from(levelDomains.values())
                    .flat()
                    .flatMap(level => level.id as string)
            )
    })

    const updateStep = async (
        direction: 'left' | 'right',
        e: React.MouseEvent
    ) => {
        e.preventDefault()
        if (direction === 'left') {
            setStepRequest((step > 1 ? step - 1 : step) as 1 | 2 | 3)
        } else {
            setStepRequest((step < 3 ? step + 1 : step) as 1 | 2 | 3)
            if (step === 3) {
                mutate()
                toast.message(t('register.request_sent'), {
                    duration: 1500,
                    onAutoClose: () => {
                        setOpen(false)
                        clearNeeds()
                    }
                })
            }
        }
    }
    const disabledNext =
        step === 1
            ? domainsLength < 1
            : step === 2
              ? lengthLD < 1
              : isEmptyOrNull(explanation)
    const stepsViews = [
        <></>,
        <DomainsSelection
            key="DomainsSelection"
            domains={domains}
            isExtension
            setDomains={setDomainsNeeds}
        />,
        <LevelsSelection
            key="LevelsSelection"
            domains={domains}
            levelDomains={levelDomains}
            isExtension
            setLevelDomains={setLevelDomainsNeeds}
        />,
        <ExplanationStep key="ExplanationStep" />,
        <></>
    ]
    return (
        <Dialog
            open={open}
            onOpenChange={open => {
                setStepRequest(1)
                setOpen(open)
            }}
        >
            <DialogTrigger asChild>
                <Button
                    size="sm"
                    className="bg-dinoBotBlue hover:bg-dinoBotVibrantBlue "
                >
                    {t('register.ask')}
                </Button>
            </DialogTrigger>
            <DialogContent className="min-w-[1200px]">
                <DialogHeader>
                    <DialogTitle className="flex text-lg justify-center">
                        {t('register.request_title')}
                    </DialogTitle>
                    <DialogDescription></DialogDescription>
                </DialogHeader>
                {stepsViews.at(step)}
                <DialogFooter className="w-full flex sm:justify-between items-center mt-4">
                    <Button
                        onClick={e => updateStep('left', e)}
                        className={cn(
                            'bg-dinoBotBlue hover:bg-dinoBotVibrantBlue rounded-xl',
                            { invisible: step <= 1 }
                        )}
                    >
                        {t('register.previous')}
                    </Button>
                    <div className="flex flex-col items-center">
                        <span className="text-dinoBotGray text-sm">
                            {t('register.formule')}
                        </span>
                    </div>
                    <Button
                        onClick={e => updateStep('right', e)}
                        disabled={disabledNext}
                        className="bg-dinoBotBlue hover:bg-dinoBotVibrantBlue rounded-xl"
                    >
                        {t('register.next_save', { step })}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

export default CommentsNeeds

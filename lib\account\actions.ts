'use server'

import { getUser, signOut } from '@/auth'
import { auth } from '@/auth' // ou selon ton système
import { checkAuth } from '../utils/auth-utils'
import { getStringFromBuffer } from '../utils/utils'
import {
    sendAccountDeletionEmail,
    sendAccountReactivationEmail
} from '../mail-utils'

import * as server from './server'
import {
    EmptyPasswordError,
    InvalidPasswordError,
    NotFoundError
} from '../utils/errors'
import { logger } from '@/logger/logger'
import { User } from '@prisma/client'
import { redirect } from '@/i18n/routing'
import { getLocale } from 'next-intl/server'

type ActionResult<T = undefined> = T | { error: string; status?: number }

export async function disableMyAccount(
    password: string
): Promise<ActionResult<User>> {
    try {
        const session = await checkAuth()
        const email = session.user?.email

        if (!email) {
            throw new NotFoundError('User email not found')
        }

        const user = await getUser(email)

        if (!user) {
            throw new NotFoundError('User not found')
        }

        if (await isValidPassword(user.password, password, user.salt)) {
            const account = (await server.disableAccount(email)) as User
            await sendAccountDeletionEmail(email, '')
            return account
        }

        throw new Error()
    } catch (error) {
        console.error('Error disabling account:', error)
        if (
            error instanceof EmptyPasswordError ||
            error instanceof InvalidPasswordError
        ) {
            return { error: error.message, status: 400 }
        }
        if (error instanceof NotFoundError) {
            return { error: error.message, status: 404 }
        }
        return { error: 'error-occurred', status: 500 }
    }
}

async function isValidPassword(
    storedHashedPassword: string,
    inputPassword: string,
    salt: string
) {
    try {
        if (inputPassword === '') {
            throw new EmptyPasswordError('empty-password')
        }

        const encoder = new TextEncoder()
        const saltedPassword = encoder.encode(inputPassword + salt)

        const hashedPasswordBuffer = await crypto.subtle.digest(
            'SHA-256',
            saltedPassword
        )
        const hashedPassword = getStringFromBuffer(hashedPasswordBuffer)

        if (hashedPassword !== storedHashedPassword) {
            throw new InvalidPasswordError('incorrect-password')
        }

        return true
    } catch (error) {
        logger.error(`Error validating password: ${error}`)
        throw error
    }
}

export type EnableAccountResult =
    | { data: User; error?: null; status?: number }
    | { data?: null; error: string; status: number }

export async function enableAccount(
    password: string
): Promise<EnableAccountResult> {
    try {
        const session = await checkAuth()
        const email = session.user?.email
        if (!email) {
            throw new NotFoundError('User email not found')
        }

        const user = await getUser(email)

        if (!user) {
            throw new NotFoundError('User not found')
        }

        if (await isValidPassword(user.password, password, user.salt)) {
            const account = (await server.enableAccount(email)) as User
            await sendAccountReactivationEmail(email, '')
            return { data: account, status: 200 }
        }

        return { error: 'incorrect-password', status: 401 }
    } catch (error) {
        if (error instanceof NotFoundError) {
            return { error: error.message, status: 404 }
        }
        if (
            error instanceof EmptyPasswordError ||
            error instanceof InvalidPasswordError
        ) {
            return { error: error.message, status: 400 }
        }
        return { error: 'error-occurred', status: 500 }
    }
}

export async function getDeletionDate() {
    try {
        const session = await checkAuth()
        const email = session.user?.email
        if (!email) {
            throw new NotFoundError('User email not found')
        }
        const response = await server.getDeletionDate(email)

        return response?.scheduledDeletionAt
    } catch (error) {
        if (error instanceof NotFoundError) {
            return { error: error.message, status: 404 }
        }
        return { error: 'error-occurred', status: 500 }
    }
}

export const disconnect = async () => {
    const local = await getLocale()
    await signOut()
    redirect({ href: '/', locale: local })
}
